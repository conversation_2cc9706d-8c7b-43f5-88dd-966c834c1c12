import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Package, Wrench, HardHat, Truck, Plus, ShoppingBag, X } from 'lucide-react';
import toast from 'react-hot-toast';
import { templateItemsApi, templatesCreationApi } from '../../../services/api';

const TEMPLATE_TYPES = ['Equipment', 'Accessory', 'Installation', 'Service'] as const;
type TemplateType = typeof TEMPLATE_TYPES[number];

// Type definitions for form data
interface EquipmentFormData {
  itemName: string;
  specification: string;
  quantity: number;
  quantityType: 'individual' | 'set';
  price: number;
}

interface AccessoryFormData {
  itemName: string;
  quantity: number;
  quantityType: 'individual' | 'set';
  amount: number;
}

interface InstallationFormData {
  name: string;
  price: number;
}

interface ServiceFormData {
  serviceName: string;
  areaInSquareFeet: number;
  pricePerSquareFoot: number;
  totalPrice: number;
}

const AddTemplate = () => {
  const navigate = useNavigate();  const [selectedType, setSelectedType] = useState<TemplateType | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  
  // Local state for storing items before API submission
  const [localEquipmentItems, setLocalEquipmentItems] = useState<EquipmentFormData[]>([]);
  const [localAccessoryItems, setLocalAccessoryItems] = useState<AccessoryFormData[]>([]);
  const [localInstallationItems, setLocalInstallationItems] = useState<InstallationFormData[]>([]);
  const [localServiceItems, setLocalServiceItems] = useState<ServiceFormData[]>([]);
  
  const [showItemForm, setShowItemForm] = useState(false);
  const [predefinedItems, setPredefinedItems] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoadingPredefinedItems, setIsLoadingPredefinedItems] = useState(false);
  const [selectedPredefinedItem, setSelectedPredefinedItem] = useState<any | null>(null);
  const [showPredefinedDropdown, setShowPredefinedDropdown] = useState(false);  // Equipment form data
  const [equipmentFormData, setEquipmentFormData] = useState<EquipmentFormData>({
    itemName: '',
    specification: '',
    quantity: 1,
    quantityType: 'individual',
    price: 0,
  });
  
  // Accessory form data
  const [accessoryFormData, setAccessoryFormData] = useState<AccessoryFormData>({
    itemName: '',
    quantity: 1,
    quantityType: 'individual',
    amount: 0,
  });
  
  // Installation form data
  const [installationFormData, setInstallationFormData] = useState<InstallationFormData>({
    name: '',
    price: 0,
  });
  
  // Service form data
  const [serviceFormData, setServiceFormData] = useState<ServiceFormData>({
    serviceName: '',
    areaInSquareFeet: 0,
    pricePerSquareFoot: 0,
    totalPrice: 0,
  });

  const getTemplateIcon = (type: TemplateType) => {
    switch (type) {
      case 'Equipment': return <Package className="h-8 w-8" />;
      case 'Accessory': return <Wrench className="h-8 w-8" />;
      case 'Installation': return <HardHat className="h-8 w-8" />;
      case 'Service': return <Truck className="h-8 w-8" />;
      default: return <Package className="h-8 w-8" />;
    }
  };
  const getTemplateColor = (type: TemplateType) => {
    switch (type) {
      case 'Equipment': return 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200';
      case 'Accessory': return 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200';
      case 'Installation': return 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200';
      case 'Service': return 'bg-pink-100 text-pink-800 border-pink-200 hover:bg-pink-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200';
    }
  };  // Load available items and reset form when template type changes
  useEffect(() => {
    const resetForm = () => {
      // Reset all form data states
      setEquipmentFormData({
        itemName: '',
        specification: '',
        quantity: 1,
        quantityType: 'individual',
        price: 0,
      });
      
      setAccessoryFormData({
        itemName: '',
        quantity: 1,
        quantityType: 'individual',
        amount: 0,
      });
      
      setInstallationFormData({
        name: '',
        price: 0,
      });
      
      setServiceFormData({
        serviceName: '',
        areaInSquareFeet: 0,
        pricePerSquareFoot: 0,
        totalPrice: 0,
      });
      
      // Reset local item arrays
      setLocalEquipmentItems([]);
      setLocalAccessoryItems([]);
      setLocalInstallationItems([]);
      setLocalServiceItems([]);
      
      // Reset selected items
      setSelectedItems([]);
      
      setSelectedPredefinedItem(null);
      setSearchTerm('');
    };
    
    if (selectedType) {
      resetForm();
      loadPredefinedItems();
    }
  }, [selectedType]);
    // Add click outside listener to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.predefined-dropdown-container')) {
        setShowPredefinedDropdown(false);
      }
    };
    
    document.addEventListener('click', handleClickOutside);
    
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);
  
  // Reset appropriate form data when template type changes
  useEffect(() => {
    if (selectedType) {
      switch (selectedType) {
        case 'Equipment':
          setEquipmentFormData({
            itemName: '',
            specification: '',
            quantity: 1,
            quantityType: 'individual',
            price: 0,
          });
          break;
        case 'Accessory':
          setAccessoryFormData({
            itemName: '',
            quantity: 1,
            quantityType: 'individual',
            amount: 0,
          });
          break;
        case 'Installation':
          setInstallationFormData({
            name: '',
            price: 0,
          });
          break;
        case 'Service':
          setServiceFormData({
            serviceName: '',
            areaInSquareFeet: 0,
            pricePerSquareFoot: 0,
            totalPrice: 0,
          });
          break;
      }
    }
  }, [selectedType]);
    
  // Load predefined items based on the selected template type
  const loadPredefinedItems = async () => {
    if (!selectedType) return;
    
    setIsLoadingPredefinedItems(true);
    try {
      let response;
      
      switch (selectedType) {
        case 'Equipment':
          response = await templateItemsApi.getEquipmentItems();
          break;
        case 'Accessory':
          response = await templateItemsApi.getAccessoryItems();
          break;
        case 'Installation':
          response = await templateItemsApi.getInstallationItems();
          break;
        case 'Service':
          response = await templateItemsApi.getServiceItems();
          break;
        default:
          throw new Error('Invalid template type');
      }
      
      // Extract items from the response, assuming the API returns { data: { items: any[] } }
      const items = response?.data?.items || [];
      setPredefinedItems(items);
    } catch (error) {
      console.error('Error loading predefined items:', error);
      toast.error('Failed to load predefined items');
      setPredefinedItems([]);
    } finally {
      setIsLoadingPredefinedItems(false);
    }
  };

  const handleAddItem = () => {
    if (!selectedType) {
      toast.error('Please select a template type first');
      return;
    }
    setShowItemForm(true);
  };
  const handleCancelItemForm = () => {
    setShowItemForm(false);
    
    // Reset form data based on selected type
    switch (selectedType) {
      case 'Equipment':
        setEquipmentFormData({
          itemName: '',
          specification: '',
          quantity: 1,
          quantityType: 'individual',
          price: 0,
        });
        break;
      case 'Accessory':
        setAccessoryFormData({
          itemName: '',
          quantity: 1,
          quantityType: 'individual',
          amount: 0,
        });
        break;
      case 'Installation':
        setInstallationFormData({
          name: '',
          price: 0,
        });
        break;
      case 'Service':
        setServiceFormData({
          serviceName: '',
          areaInSquareFeet: 0,
          pricePerSquareFoot: 0,
          totalPrice: 0,
        });
        break;
    }
  };
  const handleItemFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const parsedValue = type === 'number' ? parseFloat(value) || 0 : value;
    
    // Update the appropriate form data based on the selected type
    switch (selectedType) {
      case 'Equipment':
        setEquipmentFormData(prev => ({
          ...prev,
          [name]: parsedValue
        }));
        break;
        
      case 'Accessory':
        setAccessoryFormData(prev => ({
          ...prev,
          [name]: parsedValue
        }));
        break;
        
      case 'Installation':
        setInstallationFormData(prev => ({
          ...prev,
          [name]: parsedValue
        }));
        break;
        
      case 'Service':
        setServiceFormData(prev => {
          const newData = { ...prev, [name]: parsedValue };
          
          // Auto-calculate totalPrice for Service items
          if (name === 'areaInSquareFeet' || name === 'pricePerSquareFoot') {
            newData.totalPrice = newData.areaInSquareFeet * newData.pricePerSquareFoot;
          }
          
          return newData;
        });
        break;
    }
  };  const handleSubmitItemForm = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      let newItem: any = { _id: `temp_${Date.now()}` }; // Create a temporary ID for local storage
      
      switch (selectedType) {
        case 'Equipment':
          if (!equipmentFormData.itemName.trim()) {
            toast.error('Item name is required');
            return;
          }
          
          // Create local equipment item with temporary ID
          newItem = {
            ...newItem,
            itemName: equipmentFormData.itemName.trim(),
            specification: equipmentFormData.specification?.trim() || undefined,
            quantity: equipmentFormData.quantity,
            quantityType: equipmentFormData.quantityType,
            price: equipmentFormData.price,
            type: 'Equipment'
          };
          
          // Add to local equipment items
          setLocalEquipmentItems(prev => [...prev, {...equipmentFormData}]);
          
          // Reset form for next item
          setEquipmentFormData({
            itemName: '',
            specification: '',
            quantity: 1,
            quantityType: 'individual',
            price: 0,
          });
          break;
          
        case 'Accessory':
          if (!accessoryFormData.itemName.trim()) {
            toast.error('Item name is required');
            return;
          }
          
          // Create local accessory item with temporary ID
          newItem = {
            ...newItem,
            itemName: accessoryFormData.itemName.trim(),
            quantity: accessoryFormData.quantity,
            quantityType: accessoryFormData.quantityType,
            amount: accessoryFormData.amount,
            type: 'Accessory'
          };
          
          // Add to local accessory items
          setLocalAccessoryItems(prev => [...prev, {...accessoryFormData}]);
          
          // Reset form for next item
          setAccessoryFormData({
            itemName: '',
            quantity: 1,
            quantityType: 'individual',
            amount: 0,
          });
          break;
          
        case 'Installation':
          if (!installationFormData.name.trim()) {
            toast.error('Installation name is required');
            return;
          }
          
          // Create local installation item with temporary ID
          newItem = {
            ...newItem,
            name: installationFormData.name.trim(),
            price: installationFormData.price,
            type: 'Installation'
          };
          
          // Add to local installation items
          setLocalInstallationItems(prev => [...prev, {...installationFormData}]);
          
          // Reset form for next item
          setInstallationFormData({
            name: '',
            price: 0,
          });
          break;
          
        case 'Service':
          if (!serviceFormData.serviceName.trim()) {
            toast.error('Service name is required');
            return;
          }
          
          // Create local service item with temporary ID
          newItem = {
            ...newItem,
            serviceName: serviceFormData.serviceName.trim(),
            areaInSquareFeet: serviceFormData.areaInSquareFeet,
            pricePerSquareFoot: serviceFormData.pricePerSquareFoot,
            totalPrice: serviceFormData.totalPrice,
            type: 'Service'
          };
          
          // Add to local service items
          setLocalServiceItems(prev => [...prev, {...serviceFormData}]);
          
          // Reset form for next item
          setServiceFormData({
            serviceName: '',
            areaInSquareFeet: 0,
            pricePerSquareFoot: 0,
            totalPrice: 0,
          });
          break;
          
        default:
          toast.error('Invalid template type');
          return;
      }
      
      // Add the local item to selected items for display
      setSelectedItems(prev => [...prev, newItem]);
      toast.success(`${selectedType} item added to template`);
      
      // Form stays open for the next item
    } catch (error: any) {
      console.error('Error adding item:', error);
      toast.error(error.message || 'Failed to add item');
    }
  };  const handleRemoveItem = (itemId: string) => {
    const itemToRemove = selectedItems.find(item => item._id === itemId);
    
    if (itemToRemove) {
      // Remove from appropriate local item array based on type
      switch (itemToRemove.type) {
        case 'Equipment':
          setLocalEquipmentItems(prev => 
            prev.filter((_, index) => !isMatchingLocalItem(prev[index], itemToRemove))
          );
          break;
        case 'Accessory':
          setLocalAccessoryItems(prev => 
            prev.filter((_, index) => !isMatchingLocalItem(prev[index], itemToRemove))
          );
          break;
        case 'Installation':
          setLocalInstallationItems(prev => 
            prev.filter((_, index) => !isMatchingLocalItem(prev[index], itemToRemove))
          );
          break;
        case 'Service':
          setLocalServiceItems(prev => 
            prev.filter((_, index) => !isMatchingLocalItem(prev[index], itemToRemove))
          );
          break;
      }
    }
    
    // Remove from display list
    setSelectedItems(prev => prev.filter(item => item._id !== itemId));
    toast.success('Item removed from template');
  };
  
  // Helper function to match local item with the item in selectedItems
  const isMatchingLocalItem = (localItem: any, selectedItem: any) => {
    if (selectedItem.type === 'Equipment') {
      return localItem.itemName === selectedItem.itemName && 
             localItem.specification === selectedItem.specification;
    } else if (selectedItem.type === 'Accessory') {
      return localItem.itemName === selectedItem.itemName && 
             localItem.quantity === selectedItem.quantity;
    } else if (selectedItem.type === 'Installation') {
      return localItem.name === selectedItem.name && 
             localItem.price === selectedItem.price;
    } else if (selectedItem.type === 'Service') {
      return localItem.serviceName === selectedItem.serviceName && 
             localItem.totalPrice === selectedItem.totalPrice;
    }
    return false;
  };  
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedType) {
      toast.error('Please select a template type');
      return;
    }

    // Validate template name
    if (!formData.name.trim()) {
      toast.error('Template name is required');
      return;
    }

    try {
      // Show loading toast
      const loadingToastId = toast.loading('Creating template...');
        
      // First create all the items and collect their IDs
      const itemIds: string[] = [];
      
      // Check if we have local items based on the selected template type
      let hasLocalItems = false;
      
      switch (selectedType) {
        case 'Equipment':
          hasLocalItems = localEquipmentItems.length > 0;
          break;
        case 'Accessory':
          hasLocalItems = localAccessoryItems.length > 0;
          break;
        case 'Installation':
          hasLocalItems = localInstallationItems.length > 0;
          break;
        case 'Service':
          hasLocalItems = localServiceItems.length > 0;
          break;
      }      // Also check if there are any existing selected items of the current type
      hasLocalItems = hasLocalItems || selectedItems.some(item => 
        // Include real items (non-temp) of the current type
        (!item._id.toString().startsWith('temp_') && item.type === selectedType) ||
        // Include items with no type (likely from API)
        (!item.type && !item._id.toString().startsWith('temp_'))
      );
        
      if (!hasLocalItems) {
        toast.dismiss(loadingToastId);
        
        let errorMessage;
        switch (selectedType) {
          case 'Equipment':
            errorMessage = 'Please add at least one equipment item to the template.';
            break;
          case 'Accessory':
            errorMessage = 'Please add at least one accessory item to the template.';
            break;
          case 'Installation':
            errorMessage = 'Please add at least one installation item to the template.';
            break;
          case 'Service':
            errorMessage = 'Please add at least one service item to the template.';
            break;
          default:
            errorMessage = `Please add at least one item to the ${selectedType} template.`;
        }
        
        toast.error(errorMessage);
        return;
      }
      
      // Create items based on template type
      switch (selectedType) {
        case 'Equipment':
          // Create all local equipment items
          for (const item of localEquipmentItems) {
            const newItem = await templateItemsApi.createEquipmentItem({
              itemName: item.itemName.trim(),
              specification: item.specification?.trim() || undefined,
              quantity: item.quantity,
              quantityType: item.quantityType,
              price: item.price,
            });
            itemIds.push(newItem.data._id);
          }
          break;
          
        case 'Accessory':
          // Create all local accessory items
          for (const item of localAccessoryItems) {
            const newItem = await templateItemsApi.createAccessoryItem({
              itemName: item.itemName.trim(),
              quantity: item.quantity,
              quantityType: item.quantityType,
              amount: item.amount,
            });
            itemIds.push(newItem.data._id);
          }
          break;
          
        case 'Installation':
          // Create all local installation items
          for (const item of localInstallationItems) {
            const newItem = await templateItemsApi.createInstallationItem({
              name: item.name.trim(),
              price: item.price,
            });
            itemIds.push(newItem.data._id);
          }
          break;
          
        case 'Service':
          // Create all local service items
          for (const item of localServiceItems) {
            const newItem = await templateItemsApi.createServiceItem({
              serviceName: item.serviceName.trim(),
              areaInSquareFeet: item.areaInSquareFeet,
              pricePerSquareFoot: item.pricePerSquareFoot,
              totalPrice: item.totalPrice,
            });
            itemIds.push(newItem.data._id);
          }
          break;
          
        default:
          throw new Error('Invalid template type');
      }
        // Add any existing real items (not temp ids) that match the current template type
      const existingItemIds = selectedItems
        .filter(item => 
          !item._id.toString().startsWith('temp_') && 
          (item.type === selectedType || !item.type) // Include if type matches or if type is not specified
        )
        .map(item => item._id);
      
      // Combine all item IDs
      const allItemIds = [...itemIds, ...existingItemIds];      // Prepare template data with all items
      const templateData = {
        name: formData.name.trim(), // We've already validated this is not empty
        description: formData.description?.trim() || undefined,
        items: allItemIds,
      };

      let response;

      // Call appropriate API based on template type
      switch (selectedType) {
        case 'Equipment':
          response = await templatesCreationApi.createEquipmentTemplate(templateData);
          break;
        case 'Accessory':
          response = await templatesCreationApi.createAccessoryTemplate(templateData);
          break;
        case 'Installation':
          response = await templatesCreationApi.createInstallationTemplate(templateData);
          break;
        case 'Service':
          response = await templatesCreationApi.createServiceTemplate(templateData);
          break;
        default:
          throw new Error('Invalid template type');
      }

      toast.dismiss(loadingToastId);
      toast.success(`${selectedType} template created successfully!`);
      console.log('Template created:', response);
      navigate('/predefined');
    } catch (error: any) {
      console.error('Error creating template:', error);
      toast.error(error.message || 'Failed to create template');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate('/predefined')}
              className="flex items-center space-x-2 text-[var(--color-steel-blue)] hover:text-[var(--color-dark-indigo)] transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Templates</span>
            </button>
          </div>
          <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Add New Template</h1>
          <p className="text-[var(--color-steel-blue)] mt-2">Create a new predefined template for your projects</p>
        </div>

        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-8">
          <form onSubmit={handleSubmit}>
            {/* Template Type Selection */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-[var(--color-dark-indigo)] mb-4">Select Template Type</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {TEMPLATE_TYPES.map((type) => (
                  <button
                    key={type}
                    type="button"
                    onClick={() => setSelectedType(type)}
                    className={`p-6 border-2 rounded-xl transition-all duration-200 ${
                      selectedType === type
                        ? `${getTemplateColor(type)} border-current`
                        : 'border-[var(--color-light-grey-blue)] hover:border-[var(--color-muted-blue-grey)] hover:bg-[var(--color-light-grey-blue)]'
                    }`}
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className={`p-3 rounded-full ${
                        selectedType === type 
                          ? 'bg-white bg-opacity-50' 
                          : 'bg-[var(--color-deep-blue)] text-white'
                      }`}>
                        {getTemplateIcon(type)}
                      </div>
                      <span className="font-medium">{type}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Template Details */}
            {selectedType && (
              <div className="space-y-6">
                <div className="border-t border-[var(--color-light-grey-blue)] pt-6">
                  <h2 className="text-xl font-semibold text-[var(--color-dark-indigo)] mb-4">
                    {selectedType} Template Details
                  </h2>
                </div>                {/* Template Name */}                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                    Template Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}                    placeholder={`Enter a name for your template`}
                    className="w-full px-4 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors"
                    required
                  />
                </div>                {/* Template Description */}
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder={`Describe this ${selectedType.toLowerCase()} template...`}
                    rows={4}
                    className="w-full px-4 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors resize-none"
                  />
                </div>                {/* Template Items Section */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)]">Template Items</h3>
                    <button
                      type="button"
                      onClick={handleAddItem}
                      className="flex items-center space-x-2 px-4 py-2 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-white rounded-lg font-medium transition-colors"
                    >
                      <ShoppingBag className="h-4 w-4" />
                      <span>Add Item</span>
                    </button>
                  </div>

                  {/* Item Creation Form */}
                  {showItemForm && (
                    <div className="mb-6 p-6 bg-[var(--color-light-grey-blue)] rounded-lg border">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold text-[var(--color-dark-indigo)]">
                          Add New {selectedType} Item
                        </h4>
                        <button
                          type="button"
                          onClick={handleCancelItemForm}
                          className="p-2 text-[var(--color-steel-blue)] hover:text-[var(--color-dark-indigo)] rounded-lg hover:bg-white transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>                      <div className="space-y-4">
                        {/* Searchable Dropdown for Predefined Items */}
                        <div className="mb-4">
                          <label htmlFor="predefinedItem" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                            Select from Predefined Items (Optional)
                          </label>                          <div className="relative predefined-dropdown-container">
                            <input 
                              type="text"
                              placeholder={`Search ${selectedType?.toLowerCase()} items...`}
                              value={searchTerm}
                              onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setShowPredefinedDropdown(true);
                              }}
                              onFocus={() => setShowPredefinedDropdown(true)}
                              className="w-full px-4 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors"
                            />
                            
                            {showPredefinedDropdown && (
                              <div className="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg max-h-60 overflow-auto">
                                {isLoadingPredefinedItems ? (
                                  <div className="p-4 text-center text-[var(--color-steel-blue)]">
                                    Loading items...
                                  </div>
                                ) : (
                                  <>
                                    {predefinedItems.length > 0 ? (
                                      predefinedItems
                                        .filter(item => {
                                          const itemName = item.itemName || item.name || item.serviceName || '';
                                          return itemName.toLowerCase().includes(searchTerm.toLowerCase());
                                        })
                                        .map((item, index) => (
                                          <div
                                            key={item._id || index}
                                            className="p-3 hover:bg-[var(--color-light-grey-blue)] cursor-pointer"                                              onClick={() => {
                                              setSelectedPredefinedItem(item);
                                              
                                              // Set form data based on item type
                                              if (selectedType === 'Equipment') {
                                                setEquipmentFormData({
                                                  itemName: item.itemName || '',
                                                  specification: item.specification || '',
                                                  quantity: item.quantity || 1,
                                                  quantityType: item.quantityType || 'individual',
                                                  price: item.price || 0
                                                });
                                              } else if (selectedType === 'Accessory') {
                                                setAccessoryFormData({
                                                  itemName: item.itemName || '',
                                                  quantity: item.quantity || 1,
                                                  quantityType: item.quantityType || 'individual',
                                                  amount: item.amount || 0
                                                });
                                              } else if (selectedType === 'Installation') {
                                                setInstallationFormData({
                                                  name: item.name || '',
                                                  price: item.price || 0
                                                });
                                              } else if (selectedType === 'Service') {
                                                setServiceFormData({
                                                  serviceName: item.serviceName || '',
                                                  areaInSquareFeet: item.areaInSquareFeet || 0,
                                                  pricePerSquareFoot: item.pricePerSquareFoot || 0,
                                                  totalPrice: item.totalPrice || 0
                                                });
                                              }
                                              
                                              // Set search term to the selected item name for display
                                              setSearchTerm(item.itemName || item.name || item.serviceName || '');
                                              setShowPredefinedDropdown(false);
                                            }}
                                          >
                                            <div className="font-medium text-[var(--color-dark-indigo)]">
                                              {item.itemName || item.name || item.serviceName}
                                            </div>
                                            <div className="text-sm text-[var(--color-steel-blue)]">
                                              {selectedType === 'Equipment' && `${item.quantity} ${item.quantityType} - $${item.price}`}
                                              {selectedType === 'Accessory' && `${item.quantity} ${item.quantityType} - $${item.amount}`}
                                              {selectedType === 'Installation' && `$${item.price}`}
                                              {selectedType === 'Service' && `${item.areaInSquareFeet} sq ft - $${item.totalPrice}`}
                                            </div>
                                          </div>
                                        ))
                                    ) : (
                                      <div className="p-4 text-center text-[var(--color-steel-blue)]">
                                        No predefined items found
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                            )}
                          </div>                          {selectedPredefinedItem && (
                            <div className="mt-2 flex justify-between items-center">
                              <span className="text-sm text-green-600">
                                Predefined item selected: {selectedPredefinedItem.itemName || selectedPredefinedItem.name || selectedPredefinedItem.serviceName}
                              </span>
                              <button 
                                type="button"
                                onClick={() => setSelectedPredefinedItem(null)}
                                className="text-xs text-red-500 hover:text-red-700"
                              >
                                Clear
                              </button>
                            </div>
                          )}
                        </div>
                          {/* Equipment Fields */}
                        {selectedType === 'Equipment' && (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="itemName" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Item Name *
                                </label>
                                <input
                                  type="text"
                                  id="itemName"
                                  name="itemName"
                                  value={equipmentFormData.itemName}
                                  onChange={handleItemFormChange}
                                  placeholder="Enter equipment name"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="specification" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Specification
                                </label>
                                <input
                                  type="text"
                                  id="specification"
                                  name="specification"
                                  value={equipmentFormData.specification}
                                  onChange={handleItemFormChange}
                                  placeholder="Enter specification"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                />
                              </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <label htmlFor="quantity" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Quantity *
                                </label>
                                <input
                                  type="number"
                                  id="quantity"
                                  name="quantity"
                                  value={equipmentFormData.quantity}
                                  onChange={handleItemFormChange}
                                  min="1"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="quantityType" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Quantity Type *
                                </label>
                                <select
                                  id="quantityType"
                                  name="quantityType"
                                  value={equipmentFormData.quantityType}
                                  onChange={handleItemFormChange}
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                >
                                  <option value="individual">Individual</option>
                                  <option value="set">Set</option>
                                </select>
                              </div>
                              <div>
                                <label htmlFor="price" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Price ($) *
                                </label>
                                <input
                                  type="number"
                                  id="price"
                                  name="price"
                                  value={equipmentFormData.price}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                            </div>
                          </>
                        )}                        {/* Accessory Fields */}
                        {selectedType === 'Accessory' && (
                          <>
                            <div>
                              <label htmlFor="itemName" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                Item Name *
                              </label>
                              <input
                                type="text"
                                id="itemName"
                                name="itemName"
                                value={accessoryFormData.itemName}
                                onChange={handleItemFormChange}
                                placeholder="Enter accessory name"
                                className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                required
                              />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <label htmlFor="quantity" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Quantity *
                                </label>
                                <input
                                  type="number"
                                  id="quantity"
                                  name="quantity"
                                  value={accessoryFormData.quantity}
                                  onChange={handleItemFormChange}
                                  min="1"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="quantityType" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Quantity Type *
                                </label>
                                <select
                                  id="quantityType"
                                  name="quantityType"
                                  value={accessoryFormData.quantityType}
                                  onChange={handleItemFormChange}
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                >
                                  <option value="individual">Individual</option>
                                  <option value="set">Set</option>
                                </select>
                              </div>
                              <div>
                                <label htmlFor="amount" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Amount ($) *
                                </label>
                                <input
                                  type="number"
                                  id="amount"
                                  name="amount"
                                  value={accessoryFormData.amount}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                            </div>
                          </>
                        )}                        {/* Installation Fields */}
                        {selectedType === 'Installation' && (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="name" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Installation Name *
                                </label>
                                <input
                                  type="text"
                                  id="name"
                                  name="name"
                                  value={installationFormData.name}
                                  onChange={handleItemFormChange}
                                  placeholder="Enter installation name"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="price" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Price ($) *
                                </label>
                                <input
                                  type="number"
                                  id="price"
                                  name="price"
                                  value={installationFormData.price}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                            </div>
                          </>
                        )}                        {/* Service Fields */}
                        {selectedType === 'Service' && (
                          <>
                            <div>
                              <label htmlFor="serviceName" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                Service Name *
                              </label>
                              <input
                                type="text"
                                id="serviceName"
                                name="serviceName"
                                value={serviceFormData.serviceName}
                                onChange={handleItemFormChange}
                                placeholder="Enter service name"
                                className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                required
                              />
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <label htmlFor="areaInSquareFeet" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Area (sq ft) *
                                </label>
                                <input
                                  type="number"
                                  id="areaInSquareFeet"
                                  name="areaInSquareFeet"
                                  value={serviceFormData.areaInSquareFeet}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="pricePerSquareFoot" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Price per sq ft ($) *
                                </label>
                                <input
                                  type="number"
                                  id="pricePerSquareFoot"
                                  name="pricePerSquareFoot"
                                  value={serviceFormData.pricePerSquareFoot}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
                                  required
                                />
                              </div>
                              <div>
                                <label htmlFor="totalPrice" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                                  Total Price ($)
                                </label>
                                <input
                                  type="number"
                                  id="totalPrice"
                                  name="totalPrice"
                                  value={serviceFormData.totalPrice}
                                  onChange={handleItemFormChange}
                                  min="0"
                                  step="0.01"
                                  className="w-full px-3 py-2 border border-[var(--color-muted-blue-grey)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent bg-gray-50"
                                  readOnly
                                />
                              </div>
                            </div>
                          </>
                        )}<div className="flex justify-end pt-4">
                          <button
                            type="button"
                            onClick={handleSubmitItemForm}
                            className="px-4 py-2 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-white rounded-lg font-medium transition-colors"
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Selected Items Display */}
                  {selectedItems.length > 0 ? (
                    <div className="space-y-3">
                      {selectedItems.map((item, index) => (                        <div 
                          key={item._id || index} 
                          className={`flex items-center justify-between p-4 rounded-lg border ${
                            item.type === selectedType || !item.type
                              ? 'bg-[var(--color-light-grey-blue)]' 
                              : 'bg-gray-100 opacity-60'
                          }`}
                        >
                          <div className="flex-1">
                            <div className="flex items-center">
                              <h4 className="font-medium text-[var(--color-dark-indigo)]">
                                {item.itemName || item.name || item.serviceName}
                              </h4>
                              {item.type !== selectedType && item.type && (
                                <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded">
                                  Not used in this template type
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-[var(--color-steel-blue)]">
                              {item.type === 'Equipment' && `${item.quantity} ${item.quantityType} - $${item.price}`}
                              {item.type === 'Accessory' && `${item.quantity} ${item.quantityType} - $${item.amount}`}
                              {item.type === 'Installation' && `$${item.price}`}
                              {item.type === 'Service' && `${item.areaInSquareFeet} sq ft - $${item.totalPrice}`}
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveItem(item._id)}
                            className="ml-4 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-[var(--color-steel-blue)]">
                      <ShoppingBag className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p>No items added yet. Click "Add Item" to get started.</p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-[var(--color-light-grey-blue]">
                  <button
                    type="button"
                    onClick={() => navigate('/predefined')}
                    className="px-6 py-3 text-[var(--color-steel-blue)] bg-[var(--color-light-grey-blue)] hover:bg-[var(--color-muted-blue-grey)] rounded-lg font-medium transition-colors"
                  >
                    Cancel
                  </button>                    <div
                    onClick={(e) => handleSubmit(e)}
                    title="Template name and at least one item are required"
                    className="px-6 py-3 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-white rounded-lg font-medium transition-colors flex items-center space-x-2 cursor-pointer"
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleSubmit(e);
                      }
                    }}
                  >
                    <Plus className="h-5 w-5" />
                    <span>Create Template</span>
                  </div>
                </div>
              </div>
            )}          </form>
        </div>
      </div>
    </div>
  );
};

export default AddTemplate;