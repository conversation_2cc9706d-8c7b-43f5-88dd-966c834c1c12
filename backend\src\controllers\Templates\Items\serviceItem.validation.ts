import { z } from 'zod';

// Create service item schema
export const createServiceItemSchema = z.object({
  serviceName: z.string({
    required_error: 'Service name is required',
    invalid_type_error: 'Service name must be a string',
  }).min(3, { message: 'Service name must be at least 3 characters long' })
    .max(100, { message: 'Service name cannot exceed 100 characters' })
    .trim(),
  areaInSquareFeet: z.number({
    required_error: 'Area in square feet is required',
    invalid_type_error: 'Area in square feet must be a number',
  }).min(0, { message: 'Area cannot be negative' }),
  pricePerSquareFoot: z.number({
    required_error: 'Price per square foot is required',
    invalid_type_error: 'Price per square foot must be a number',
  }).min(0, { message: 'Price per square foot cannot be negative' }),
  // totalPrice is calculated automatically, so it's optional in the create schema
  totalPrice: z.number()
    .min(0, { message: 'Total price cannot be negative' })
    .optional(),
});

// Update service item schema (all fields optional)
export const updateServiceItemSchema = z.object({
  serviceName: z.string()
    .min(3, { message: 'Service name must be at least 3 characters long' })
    .max(100, { message: 'Service name cannot exceed 100 characters' })
    .trim()
    .optional(),
  areaInSquareFeet: z.number()
    .min(0, { message: 'Area cannot be negative' })
    .optional(),
  pricePerSquareFoot: z.number()
    .min(0, { message: 'Price per square foot cannot be negative' })
    .optional(),
  totalPrice: z.number()
    .min(0, { message: 'Total price cannot be negative' })
    .optional(),
});

// Service item ID validation schema
export const serviceItemIdSchema = z.object({
  id: z.string({
    required_error: 'Service item ID is required',
    invalid_type_error: 'Service item ID must be a string',
  }).min(1, { message: 'Service item ID is required' }),
});

// Search service items schema
export const searchServiceItemSchema = z.object({
  query: z.string({
    required_error: 'Search query is required',
    invalid_type_error: 'Search query must be a string',
  }).min(1, { message: 'Search query must be at least 1 character' }),
});

// Create predefined service item schema (same as create but with isPredefined)
export const createPredefinedServiceItemSchema = createServiceItemSchema.extend({
  isPredefined: z.boolean().default(true).optional(),
});

// Update predefined service item schema
export const updatePredefinedServiceItemSchema = updateServiceItemSchema.extend({
  isPredefined: z.boolean().optional(),
});
