{"name": "backend", "version": "1.0.0", "description": "Backend for Cinepanda project", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/jsonwebtoken": "^9.0.9", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "zod": "^3.25.56"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mongoose": "^5.11.97", "@types/node": "^20.12.7", "rimraf": "^5.0.5", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}