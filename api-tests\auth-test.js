// Cinepanda Auth API Test Script using Node.js
// This script tests the authentication endpoints

const fetch = require('node-fetch');
const util = require('util');

const API_URL = 'http://localhost:8000/api';

// Helper function to make API requests
async function makeRequest(method, endpoint, data, authToken) {
  try {
    console.log(`\x1b[33mTesting: ${method} ${endpoint}\x1b[0m`);
    if (data) {
      console.log(`Request body: ${JSON.stringify(data, null, 2)}`);
    }

    const headers = {
      'Content-Type': 'application/json'
    };

    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const options = {
      method,
      headers
    };

    if (data && method !== 'GET') {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${API_URL}${endpoint}`, options);
    const responseData = await response.json();

    console.log('\nResponse:');
    console.log(util.inspect(responseData, { colors: true, depth: null }));
    console.log();

    return responseData;
  } catch (error) {
    console.error(`\x1b[31mError with ${method} ${endpoint}:\x1b[0m`, error.message);
    return null;
  }
}

async function runTests() {
  console.log('\x1b[34m=== Cinepanda Auth API Test Script ===\x1b[0m');
  console.log('\x1b[34mThis script will test the authentication API endpoints\x1b[0m\n');

  // 1. Register a test user
  console.log('\x1b[32m1. Registering a test user\x1b[0m');
  const registerData = {
    username: 'apitestuser',
    email: '<EMAIL>',
    password: 'apitest123',
    role: 'user'
  };
  const registerResponse = await makeRequest('POST', '/auth/register', registerData);

  // 2. Register an admin user
  console.log('\x1b[32m2. Registering an admin user\x1b[0m');
  const adminRegisterData = {
    username: 'apiadmin',
    email: '<EMAIL>',
    password: 'apiadmin123',
    role: 'admin'
  };
  const adminRegisterResponse = await makeRequest('POST', '/auth/register', adminRegisterData);

  // 3. Login with test user
  console.log('\x1b[32m3. Logging in with test user\x1b[0m');
  const loginData = {
    email: '<EMAIL>',
    password: 'apitest123'
  };
  const loginResponse = await makeRequest('POST', '/auth/login', loginData);

  if (!loginResponse || !loginResponse.data || !loginResponse.data.token) {
    console.log('\x1b[31mFailed to get token from login response. Can\'t continue with authenticated requests.\x1b[0m');
    return;
  }

  const token = loginResponse.data.token;
  console.log(`\x1b[32mToken extracted:\x1b[0m ${token.substring(0, 20)}... (truncated)\n`);

  // 4. Get current user information using the token
  console.log('\x1b[32m4. Getting current user information\x1b[0m');
  const currentUserResponse = await makeRequest('GET', '/auth/me', null, token);

  // 5. Login with admin user
  console.log('\x1b[32m5. Logging in with admin user\x1b[0m');
  const adminLoginData = {
    email: '<EMAIL>',
    password: 'apiadmin123'
  };
  const adminLoginResponse = await makeRequest('POST', '/auth/login', adminLoginData);

  if (!adminLoginResponse || !adminLoginResponse.data || !adminLoginResponse.data.token) {
    console.log('\x1b[31mFailed to get admin token from login response.\x1b[0m');
    return;
  }

  const adminToken = adminLoginResponse.data.token;

  // 6. Get admin user information
  console.log('\x1b[32m6. Getting admin user information\x1b[0m');
  const adminUserResponse = await makeRequest('GET', '/auth/me', null, adminToken);

  console.log('\x1b[34m=== Test script complete ===\x1b[0m');
  console.log('\x1b[33mNote: The script creates two users in the database if they don\'t exist:\x1b[0m');
  console.log('  - Regular user: <EMAIL> / apitest123');
  console.log('  - Admin user: <EMAIL> / apiadmin123');
  console.log('You can use these credentials for further manual testing with Postman.');
}

// Run the tests
runTests().catch(error => {
  console.error('\x1b[31mUnexpected error:\x1b[0m', error);
});
