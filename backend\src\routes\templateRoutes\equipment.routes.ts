import { Router } from 'express';
import {
  getAllEquipmentTemplates,
  getEquipmentTemplateById,
  createEquipmentTemplate,
  updateEquipmentTemplate,
  deleteEquipmentTemplate,
  getAllPredefinedEquipmentTemplates,
  getPredefinedEquipmentTemplateById,
  createPredefinedEquipmentTemplate,
  updatePredefinedEquipmentTemplate,
  deletePredefinedEquipmentTemplate
} from '../../controllers/Templates/equipment.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const equipmentRouter = Router();

equipmentRouter.route('/')
  .get(restrictTo('admin', 'manager'), getAllEquipmentTemplates)
  .post(restrictTo('admin', 'manager'), createEquipmentTemplate);

equipmentRouter.route('/:id')
  .get(restrictTo('admin', 'manager'), getEquipmentTemplateById)
  .put(restrictTo('admin', 'manager'), updateEquipmentTemplate)
  .delete(restrictTo('admin'), deleteEquipmentTemplate);

export default equipmentRouter;
