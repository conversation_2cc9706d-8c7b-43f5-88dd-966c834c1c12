import { Request, Response, NextFunction } from 'express';
import User, { IUser } from '../../models/User';
import bcryptjs from 'bcryptjs'; // Changed from bcrypt to bcryptjs
import * as jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import { RegisterUserInput, LoginUserInput } from './auth.validation'; // Zod schemas for validation
import { sendSuccess, sendError } from '../../utils/responseHandlers';
import dotenv from 'dotenv';

dotenv.config();

// Configure JWT settings
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d'; // Fallback for expiration is acceptable

if (!JWT_SECRET) {
  console.error(
    'CRITICAL SECURITY WARNING: JWT_SECRET is not set in environment variables. ' +
      'The application may be insecure and token generation will fail. ' +
      'Please set a strong JWT_SECRET in your .env file.'
  );
  // Note: In a production setup, the application should ideally fail to start if JWT_SECRET is missing.
}

/**
 * Generates a JWT token for a user.
 * @param userId - The ID of the user.
 * @returns The generated JWT token.
 * @throws Error if JWT_SECRET is not configured.
 */
const generateToken = (userId: string): string => {
  if (!JWT_SECRET) {
    // This error will be caught by the try/catch blocks in registerUser/loginUser
    // and should result in a 500 server error, which is appropriate.
    throw new Error('JWT secret is not configured. Cannot generate token.');
  }
  
  const options: jwt.SignOptions = {
    expiresIn: JWT_EXPIRES_IN
  };
  
  return jwt.sign({ id: userId }, JWT_SECRET, options);
};

/**
 * @description Register a new user
 * @route POST /api/auth/register
 * @access Public
 */
export const registerUser = async (
  req: Request<{}, {}, RegisterUserInput>,
  res: Response
) => {
  const { username, email, password, role } = req.body;

  try {
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return sendError(res, 409, 'User already exists with this email.');
    }

    // Create new user (password will be hashed by pre-save hook in User model)
    const newUser = new User({
      username,
      email,
      passwordHash: password, // Pass plain password, it will be hashed by the model
      role: role || 'user', // Default to 'user' if not provided
    });    await newUser.save();

    // Generate token
    const token = generateToken((newUser._id as mongoose.Types.ObjectId).toString());

    // Exclude passwordHash from the response
    const userResponse = newUser.toObject();
    // Using TypeScript cast since we know the structure
    const userResponseWithoutPassword = { ...userResponse } as Partial<typeof userResponse>;
    if ('passwordHash' in userResponseWithoutPassword) {
      delete userResponseWithoutPassword.passwordHash;
    }    return sendSuccess(
      res,
      201,
      { user: userResponseWithoutPassword, token },
      'User registered successfully.'
    );
  } catch (error: any) {
    console.error('Error during registration:', error);
    if (error.name === 'ValidationError') {
      return sendError(res, 400, 'Validation Error', error.errors);
    }
    return sendError(res, 500, 'Server error during registration.', error.message);
  }
};

/**
 * @description Authenticate a user and get token
 * @route POST /api/auth/login
 * @access Public
 */
export const loginUser = async (
  req: Request<{}, {}, LoginUserInput>,
  res: Response
) => {
  const { email, password } = req.body;

  try {
    // Check if user exists
    const user = await User.findOne({ email }).select('+passwordHash'); // Explicitly select passwordHash
    if (!user) {
      return sendError(res, 401, 'Invalid credentials. User not found.');
    }    // Check if password matches
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return sendError(res, 401, 'Invalid credentials. Password incorrect.');
    }    // Generate token
    const token = generateToken((user._id as mongoose.Types.ObjectId).toString());

    // Exclude passwordHash from the response
    const userResponse = user.toObject();
    // Using TypeScript cast since we know the structure
    const userResponseWithoutPassword = { ...userResponse } as Partial<typeof userResponse>;
    if ('passwordHash' in userResponseWithoutPassword) {
      delete userResponseWithoutPassword.passwordHash;
    }

    return sendSuccess(res, 200, { user: userResponseWithoutPassword, token }, 'Login successful.');
  } catch (error: any) {
    console.error('Error during login:', error);
    return sendError(res, 500, 'Server error during login.', error.message);
  }
};

/**
 * @description Get current logged-in user details
 * @route GET /api/auth/me
 * @access Private (requires authentication middleware)
 */
export const getMe = async (req: Request, res: Response) => {
  const userId = req.user?.id;

  if (!userId) {
    return sendError(res, 401, 'Not authorized, no user ID found.');
  }

  try {
    const user = await User.findById(userId).select('-passwordHash'); // Exclude passwordHash

    if (!user) {
      return sendError(res, 404, 'User not found.');
    }

    return sendSuccess(res, 200, { user }, 'User details fetched successfully.');
  } catch (error: any) {
    console.error('Error fetching user details:', error);
    return sendError(res, 500, 'Server error fetching user details.', error.message);
  }
};

// TODO: Add a refresh token controller if needed
// TODO: Add a logout controller (e.g., if using httpOnly cookies for tokens or blacklisting tokens)
