import { z } from 'zod';
import { ProjectStatus, ProjectStage } from '../../models/Project';

// Convert enum values to arrays for validation
const statusValues = Object.values(ProjectStatus) as [string, ...string[]];
const stageValues = Object.values(ProjectStage) as [string, ...string[]];

// Create project schema
export const createProjectSchema = z.object({
  client: z.string({
    required_error: 'Client ID is required',
    invalid_type_error: 'Client ID must be a string',
  }),
  projectName: z.string({
    required_error: 'Project name is required',
    invalid_type_error: 'Project name must be a string',
  }).min(2, { message: 'Project name must be at least 2 characters long' })
    .max(100, { message: 'Project name cannot exceed 100 characters' }),
  projectDescription: z.string().nullable().optional(),
  followupCallDate: z.coerce.date().nullable().optional(),
  startDate: z.coerce.date().nullable().optional(),
  endDate: z.coerce.date().nullable().optional(),
  status: z.enum(statusValues).default(ProjectStatus.PENDING),
  stage: z.enum(stageValues).default(ProjectStage.QUOTATION),
});

// Update project schema (similar to create but all fields optional)
export const updateProjectSchema = z.object({
  client: z.string().optional(),
  projectName: z.string()
    .min(2, { message: 'Project name must be at least 2 characters long' })
    .max(100, { message: 'Project name cannot exceed 100 characters' })
    .optional(),
  projectDescription: z.string().nullable().optional(),
  followupCallDate: z.coerce.date().nullable().optional(),
  startDate: z.coerce.date().nullable().optional(),
  endDate: z.coerce.date().nullable().optional(),
  status: z.enum(statusValues).optional(),
  stage: z.enum(stageValues).optional(),
  amountReceived: z.number().min(0).optional(),
});

// Project status update schema
export const updateStatusSchema = z.object({
  status: z.enum(statusValues, {
    required_error: 'Status is required',
    invalid_type_error: `Status must be one of: ${statusValues.join(', ')}`,
  }),
});

// Project ID parameter schema
export const projectIdSchema = z.object({
  id: z.string({
    required_error: 'Project ID is required',
    invalid_type_error: 'Project ID must be a string',
  })
  .length(24, { message: 'Invalid project ID format' })
  .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid project ID format' }),
});
