import { Router } from 'express';
import {
    getAllEquipmentItems,
    getEquipmentItemById,
    createEquipmentItem,
    updateEquipmentItem,
    deleteEquipmentItem,
    searchEquipmentItems,
    getAllPredefinedEquipmentItems,
    getPredefinedEquipmentItemById,
    createPredefinedEquipmentItem,
    updatePredefinedEquipmentItem,
    deletePredefinedEquipmentItem,
    searchPredefinedEquipmentItems
  } from '../../../controllers/Templates/Items/equipmentItem.controller';
import { restrictTo } from '../../../middleware/auth.middleware';

const equipmentItemsRouter = Router();

equipmentItemsRouter.route('/')
    .get(restrictTo('admin', 'manager', 'user'), getAllEquipmentItems)
    .post(restrictTo('admin', 'manager'), createEquipmentItem);

equipmentItemsRouter.route('/search').get(restrictTo('admin', 'manager', 'user'), searchEquipmentItems);

equipmentItemsRouter.route('/:id')
    .get(restrictTo('admin', 'manager', 'user'), getEquipmentItemById)
    .put(restrictTo('admin', 'manager'), updateEquipmentItem)
    .delete(restrictTo('admin', 'manager'), deleteEquipmentItem);

export default equipmentItemsRouter;
