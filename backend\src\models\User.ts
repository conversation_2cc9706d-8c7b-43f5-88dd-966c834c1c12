import mongoose, { Schema, Document } from 'mongoose';
import bcryptjs from 'bcryptjs'; // Import bcryptjs

// Define an interface representing a document in MongoDB.
export interface IUser extends Document {
  username: string;
  email: string;
  passwordHash: string; // Storing password hash instead of plain text
  role: 'user' | 'admin' | 'editor'; // Define possible roles
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>; // Add method to interface
}

// Define the schema corresponding to the document interface.
const UserSchema: Schema<IUser> = new Schema(
  {
    username: {
      type: String,
      required: [true, 'Username is required'],
      unique: true,
      trim: true,
      minlength: [3, 'Username must be at least 3 characters long'],
      maxlength: [30, 'Username cannot exceed 30 characters'],
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, 'Please use a valid email address'],
    },
    passwordHash: {
      type: String,
      required: [true, 'Password is required'],
      select: false, // Ensure passwordHash is not returned by default in queries
    },
    role: {
      type: String,
      enum: ['user', 'admin', 'editor'],
      default: 'user',
      required: [true, 'Role is required'],
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Pre-save hook for password hashing
UserSchema.pre<IUser>('save', async function (next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('passwordHash')) {
    return next();
  }
  try {
    const salt = await bcryptjs.genSalt(10);
    this.passwordHash = await bcryptjs.hash(this.passwordHash, salt);
    return next();
  } catch (err: any) {
    return next(err);
  }
});

// Method to compare candidate password with the stored hash
UserSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcryptjs.compare(candidatePassword, this.passwordHash);
};

// Create and export the Mongoose model.
const User = mongoose.model<IUser>('User', UserSchema);

export default User;
