# Cinepanda API Tests

This directory contains tools for testing the Cinepanda API endpoints.

## Contents

1. **Postman Collections**
   - `cinepanda.postman_collection.json` - Complete API collection with organized folders
   - `auth.postman_collection.json` - Authentication endpoints only (legacy)

2. **Testing Scripts**
   - `api-test.js` - Node.js script to test all API endpoints
   - `auth-test.js` - Node.js script to test authentication endpoints
   - `auth-test.sh` - <PERSON>sh script to test authentication endpoints
   - `run-tests.bat` - Windows batch file to run the JS test script

3. **Documentation**
   - `api-testing-guide.md` - Guide for using the Postman collections

## Getting Started

### With Postman

1. Import the `cinepanda.postman_collection.json` file into Postman
2. Ensure the Cinepanda backend server is running (default: http://localhost:8000)
3. Use the collection to test various API endpoints
4. After successful login, save the JWT token in the collection variables to use with protected endpoints

### With Command Line Scripts

#### For Windows Users:
```
.\run-tests.bat
```

#### For Linux/macOS Users:
```bash
chmod +x auth-test.sh
./auth-test.sh
```

#### For Node.js:
```bash
node auth-test.js
```

## API Structure

The API is organized into the following main sections:

1. **Authentication** (`/api/auth/`)
   - Register - POST `/api/auth/register`
   - Login - POST `/api/auth/login`
   - Get Current User - GET `/api/auth/me`

2. **Projects** (`/api/projects/`)
   - Get All Projects - GET `/api/projects`
   - Get Project by ID - GET `/api/projects/:id`
   - Create Project - POST `/api/projects`
   - Update Project - PUT `/api/projects/:id`
   - Delete Project - DELETE `/api/projects/:id`
   - Update Project Status - PATCH `/api/projects/:id/status`

3. **Clients** (`/api/clients/`)
   - Get All Clients - GET `/api/clients`
   - Get Client by ID - GET `/api/clients/:id`
   - Create Client - POST `/api/clients`
   - Update Client - PUT `/api/clients/:id`
   - Delete Client - DELETE `/api/clients/:id`
   - Search Clients - GET `/api/clients/search`

4. **Templates** (`/api/templates/`)
   - **Equipment Templates** (`/api/templates/equipment`)
     - Get All - GET `/api/templates/equipment`
     - Get by ID - GET `/api/templates/equipment/:id`
     - Create - POST `/api/templates/equipment`
     - Update - PUT `/api/templates/equipment/:id`
     - Delete - DELETE `/api/templates/equipment/:id`
     - Get Predefined - GET `/api/templates/equipment/predefined`
   - **Service Templates** (`/api/templates/services`)
     - Get All - GET `/api/templates/services`
     - Get by ID - GET `/api/templates/services/:id`
     - Create - POST `/api/templates/services`
     - Update - PUT `/api/templates/services/:id`
     - Delete - DELETE `/api/templates/services/:id`
     - Get Predefined - GET `/api/templates/services/predefined`
   - **Accessory Templates** (`/api/templates/accessories`)
     - Get All - GET `/api/templates/accessories`
     - Get by ID - GET `/api/templates/accessories/:id`
     - Create - POST `/api/templates/accessories`
     - Update - PUT `/api/templates/accessories/:id`
     - Delete - DELETE `/api/templates/accessories/:id`
     - Get Predefined - GET `/api/templates/accessories/predefined`
   - **Installation Templates** (`/api/templates/installations`)
     - Get All - GET `/api/templates/installations`
     - Get by ID - GET `/api/templates/installations/:id`
     - Create - POST `/api/templates/installations`
     - Update - PUT `/api/templates/installations/:id`
     - Delete - DELETE `/api/templates/installations/:id`
     - Get Predefined - GET `/api/templates/installations/predefined`

5. **Health Check** (`/health`) - Check server status
