@echo off
echo Running Cinepanda API Tests...
echo.

rem Check if node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js and try again
    pause
    exit /b 1
)

rem Check if node-fetch is installed
echo Checking required packages...
cd ..
call npm list node-fetch >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing node-fetch package...
    call npm install --save node-fetch
)

rem Run the test scripts
echo.
echo Starting tests...
echo.

echo === Authentication Tests ===
node auth-test.js

echo.
echo === Complete API Tests ===
node api-test.js

echo.
echo Tests completed!
pause
