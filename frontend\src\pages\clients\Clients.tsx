import { useState, useEffect } from 'react'
import { Plus, Search, Filter, Users, Mail, Phone, MapPin, AlertCircle, Loader2 } from 'lucide-react'
import { Link } from 'react-router-dom'
import toast from 'react-hot-toast'
import { clientsApi, ApiError } from '../../services/api'
import type { Client } from '../../services/api'

const Clients = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  // Fetch clients from database
  const fetchClients = async () => {
    try {
      setLoading(true)
      setError(null)
      const response: any = await clientsApi.getClients() // Renamed data to response for clarity, added any type
      if (response && response.data && Array.isArray(response.data.clients)) {
        setClients(response.data.clients)
      } else {
        setClients([]) // Ensure clients is always an array
        console.warn('API did not return the expected format for clients, received:', response)
        // Optionally, set an error message if this is unexpected
        // setError('Received invalid data format from server.')
      }
    } catch (err) {
      console.error('Error fetching clients:', err)
      if (err instanceof ApiError) {
        setError(err.message)
        if (err.status === 401) {
          toast.error('Please log in to view clients')
        } else {
          toast.error(`Failed to load clients: ${err.message}`)
        }
      } else {
        setError('Failed to load clients')
        toast.error('Failed to load clients')
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchClients()
  }, [])

  const filteredClients = clients.filter(client =>
    client.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (client.email && client.email.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Helper function to generate avatar initials
  const getAvatarInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-[var(--color-deep-blue)] animate-spin mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Loading clients...</h3>
          <p className="text-[var(--color-steel-blue)]">Please wait while we fetch your client data</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Error loading clients</h3>
          <p className="text-[var(--color-steel-blue)] mb-4">{error}</p>          <button
            onClick={fetchClients}
            className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Clients</h1>
              <p className="text-[var(--color-steel-blue)] mt-2">Manage your client relationships</p>
            </div>            <Link
              to="/clients/add"
              className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <Plus className="h-5 w-5" />
              <span>Add Client</span>
            </Link>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[var(--color-muted-blue-grey)]" />
              <input
                type="text"
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
              />
            </div>
            <button className="flex items-center space-x-2 px-4 py-2 border border-[var(--color-light-grey-blue)] rounded-lg hover:bg-[var(--color-light-grey-blue)] transition-colors">
              <Filter className="h-5 w-5 text-[var(--color-steel-blue)]" />
              <span className="text-[var(--color-steel-blue)]">Filter</span>
            </button>
          </div>
        </div>        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <div key={client._id} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 hover:shadow-xl transition-shadow">
              {/* Client Header */}
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center">
                  <span className="text-[var(--color-white)] font-semibold">{getAvatarInitials(client.clientName)}</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-[var(--color-dark-indigo)]">{client.clientName}</h3>
                  <span className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                </div>
              </div>

              {/* Client Details */}
              <div className="space-y-3 mb-4">
                {client.email && (
                  <div className="flex items-center space-x-3 text-sm text-[var(--color-steel-blue)]">
                    <Mail className="h-4 w-4" />
                    <span>{client.email}</span>
                  </div>
                )}
                <div className="flex items-center space-x-3 text-sm text-[var(--color-steel-blue)]">
                  <Phone className="h-4 w-4" />
                  <span>{client.number}</span>
                </div>
                <div className="flex items-center space-x-3 text-sm text-[var(--color-steel-blue)]">
                  <MapPin className="h-4 w-4" />
                  <span>{client.place}</span>
                </div>
                <div className="flex items-center space-x-3 text-sm text-[var(--color-steel-blue)]">
                  <Users className="h-4 w-4" />
                  <span>Added {formatDate(client.createdAt)}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  View Details
                </button>
                <button className="flex-1 border border-[var(--color-light-grey-blue] hover:bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredClients.length === 0 && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-[var(--color-muted-blue-grey)] mb-4" />
            <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">No clients found</h3>
            <p className="text-[var(--color-steel-blue)]">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first client'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default Clients
