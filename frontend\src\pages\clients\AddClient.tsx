import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { User, Mail, Phone, MapPin, Home, FileText, Save, ArrowLeft } from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { clientsApi, ApiError } from '../../services/api'
import type { CreateClientData } from '../../services/api'

// Zod validation schema
const clientSchema = z.object({
  clientName: z
    .string()
    .min(1, 'Client name is required')
    .min(2, 'Client name must be at least 2 characters long')
    .max(100, 'Client name cannot exceed 100 characters')
    .trim(),
  number: z
    .string()
    .min(1, 'Contact number is required')
    .min(10, 'Contact number must be at least 10 digits')
    .max(15, 'Contact number cannot exceed 15 digits')
    .regex(/^[\+]?[0-9\s\-\(\)]+$/, 'Please enter a valid phone number'),
  email: z
    .string()
    .email('Please enter a valid email address')
    .toLowerCase()
    .optional()
    .or(z.literal('')),
  place: z
    .string()
    .min(1, 'Place is required')
    .max(100, 'Place cannot exceed 100 characters')
    .trim(),
  address: z
    .string()
    .min(1, 'Address is required')
    .max(255, 'Address cannot exceed 255 characters')
    .trim(),
  description: z
    .string()
    .max(500, 'Description cannot exceed 500 characters')
    .trim()
    .optional()
    .or(z.literal(''))
})

type ClientFormData = z.infer<typeof clientSchema>

const AddClient = () => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      clientName: '',
      number: '',
      email: '',
      place: '',
      address: '',
      description: ''
    }
  })
  const onSubmit = async (data: ClientFormData) => {
    try {
      setLoading(true)
      
      // Prepare data for API call
      const clientData: CreateClientData = {
        clientName: data.clientName,
        number: data.number,
        email: data.email || undefined,
        place: data.place,
        address: data.address,
        description: data.description || undefined
      }
      
      // Call API to create client
      await clientsApi.createClient(clientData)
      
      toast.success('Client added successfully!')
      reset() // Clear form
      navigate('/clients') // Redirect to clients list
      
    } catch (error) {
      console.error('Error adding client:', error)
      if (error instanceof ApiError) {
        if (error.status === 401) {
          toast.error('Please log in to add clients')
        } else if (error.status === 422) {
          toast.error('Please check your input and try again')
        } else {
          toast.error(`Failed to add client: ${error.message}`)
        }
      } else {
        toast.error('Failed to add client. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              to="/clients"
              className="flex items-center justify-center w-10 h-10 rounded-lg bg-[var(--color-white)] border border-[var(--color-light-grey-blue)] hover:bg-[var(--color-light-grey-blue)] transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-[var(--color-steel-blue)]" />
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">
                Add New Client
              </h1>
              <p className="text-[var(--color-steel-blue)] mt-1">
                Create a new client profile for your video production projects
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-xl border border-[var(--color-light-grey-blue)]">
          <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-6">
            {/* Client Name */}
            <div>
              <label htmlFor="clientName" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Client Name *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  {...register('clientName')}
                  type="text"
                  id="clientName"
                  className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] ${
                    errors.clientName 
                      ? 'border-red-300 focus:ring-red-500' 
                      : 'border-[var(--color-light-grey-blue)]'
                  }`}
                  placeholder="Enter client name"
                />
              </div>
              {errors.clientName && (
                <p className="mt-1 text-sm text-red-600">{errors.clientName.message}</p>
              )}
            </div>

            {/* Contact Number */}
            <div>
              <label htmlFor="number" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Contact Number *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  {...register('number')}
                  type="tel"
                  id="number"
                  className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] ${
                    errors.number 
                      ? 'border-red-300 focus:ring-red-500' 
                      : 'border-[var(--color-light-grey-blue)]'
                  }`}
                  placeholder="Enter contact number"
                />
              </div>
              {errors.number && (
                <p className="mt-1 text-sm text-red-600">{errors.number.message}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  {...register('email')}
                  type="email"
                  id="email"
                  className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] ${
                    errors.email 
                      ? 'border-red-300 focus:ring-red-500' 
                      : 'border-[var(--color-light-grey-blue)]'
                  }`}
                  placeholder="Enter email address (optional)"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* Place and Address Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Place */}
              <div>
                <label htmlFor="place" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                  Place *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                  </div>
                  <input
                    {...register('place')}
                    type="text"
                    id="place"
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] ${
                      errors.place 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-[var(--color-light-grey-blue)]'
                    }`}
                    placeholder="Enter place/city"
                  />
                </div>
                {errors.place && (
                  <p className="mt-1 text-sm text-red-600">{errors.place.message}</p>
                )}
              </div>

              {/* Address */}
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                  Address *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Home className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                  </div>
                  <input
                    {...register('address')}
                    type="text"
                    id="address"
                    className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] ${
                      errors.address 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-[var(--color-light-grey-blue)]'
                    }`}
                    placeholder="Enter full address"
                  />
                </div>
                {errors.address && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Description
              </label>
              <div className="relative">
                <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                  <FileText className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <textarea
                  {...register('description')}
                  id="description"
                  rows={4}
                  className={`w-full pl-10 pr-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)] resize-vertical ${
                    errors.description 
                      ? 'border-red-300 focus:ring-red-500' 
                      : 'border-[var(--color-light-grey-blue)]'
                  }`}
                  placeholder="Enter additional details about the client (optional)"
                />
              </div>
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-[var(--color-light-grey-blue)]">
              <button
                type="submit"
                disabled={loading}
                className="flex items-center justify-center gap-2 px-6 py-3 bg-[var(--color-deep-blue)] text-[var(--color-white)] rounded-lg font-medium hover:bg-[var(--color-dark-indigo)] focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-5 h-5" />
                {loading ? 'Adding Client...' : 'Add Client'}
              </button>
              
              <Link
                to="/clients"
                className="flex items-center justify-center gap-2 px-6 py-3 bg-[var(--color-white)] text-[var(--color-steel-blue)] border border-[var(--color-light-grey-blue)] rounded-lg font-medium hover:bg-[var(--color-light-grey-blue)] focus:outline-none focus:ring-2 focus:ring-[var(--color-steel-blue)] focus:ring-offset-2 transition-all duration-200"
              >
                Cancel
              </Link>
            </div>
          </form>
        </div>

        {/* Helper Text */}
        <div className="mt-6 p-4 bg-[var(--color-white)] rounded-lg border border-[var(--color-light-grey-blue)]">
          <p className="text-sm text-[var(--color-steel-blue)]">
            <span className="font-medium">Note:</span> Fields marked with * are required. 
            Email is optional but recommended for communication purposes.
          </p>
        </div>
      </div>
    </div>
  )
}

export default AddClient