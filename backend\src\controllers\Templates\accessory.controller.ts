import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import AccessoryTemplate from '../../models/Templates/Accessory/AccessoryTemplate';
import PredefinedAccessoryTemplate from '../../models/Templates/Accessory/Predefined/PredefinedAccessoryTemplate';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createAccessoryTemplateSchema, 
  updateAccessoryTemplateSchema, 
  templateIdSchema,
  createPredefinedAccessoryTemplateSchema,
  updatePredefinedAccessoryTemplateSchema
} from './templates.validation';

/**
 * @description Get all accessory templates
 * @route GET /api/templates/accessories
 * @access Protected (Admin, Manager)
 */
export const getAllAccessoryTemplates = async (req: Request, res: Response) => {
  try {
    const templates = await AccessoryTemplate.find()
      .populate('items')
      .sort({ createdAt: -1 });

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory templates', error);
  }
};

/**
 * @description Get a single accessory template by ID
 * @route GET /api/templates/accessories/:id
 * @access Protected (Admin, Manager)
 */
export const getAccessoryTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await AccessoryTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Accessory template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching accessory template', error);
  }
};

/**
 * @description Create a new accessory template
 * @route POST /api/templates/accessories
 * @access Protected (Admin, Manager)
 */
export const createAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const result = createAccessoryTemplateSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }    const { templateName, description, items = [] } = result.data;

    const newTemplate = await AccessoryTemplate.create({
      templateName,
      description,
      items,
    });

    // Populate items for response
    const populatedTemplate = await AccessoryTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, { 
      message: 'Accessory template created successfully', 
      template: populatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating accessory template', error);
  }
};

/**
 * @description Update an accessory template by ID
 * @route PUT /api/templates/accessories/:id
 * @access Protected (Admin, Manager)
 */
export const updateAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    // Validate request body
    const bodyResult = updateAccessoryTemplateSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const updateData = bodyResult.data;

    // Check if the template exists
    const existingTemplate = await AccessoryTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Accessory template not found');
    }

    const updatedTemplate = await AccessoryTemplate.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, { 
      message: 'Accessory template updated successfully', 
      template: updatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating accessory template', error);
  }
};

/**
 * @description Delete an accessory template by ID
 * @route DELETE /api/templates/accessories/:id
 * @access Protected (Admin)
 */
export const deleteAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await AccessoryTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Accessory template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Accessory template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting accessory template', error);
  }
};

/**
 * @description Get all predefined accessory templates
 * @route GET /api/templates/accessories/predefined
 * @access Protected (Admin, Manager, User)
 */
export const getAllPredefinedAccessoryTemplates = async (req: Request, res: Response) => {
  try {
    const templates = await PredefinedAccessoryTemplate.find()
      .populate('items')
      .sort({ name: 1 });

    return sendSuccess(res, 200, {
      count: templates.length,
      templates,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching predefined accessory templates', error);
  }
};

/**
 * @description Get a single predefined accessory template by ID
 * @route GET /api/templates/accessories/predefined/:id
 * @access Protected (Admin, Manager, User)
 */
export const getPredefinedAccessoryTemplateById = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await PredefinedAccessoryTemplate.findById(id).populate('items');
    
    if (!template) {
      return sendError(res, 404, 'Predefined accessory template not found');
    }

    return sendSuccess(res, 200, { template });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching predefined accessory template', error);
  }
};

/**
 * @description Create a new predefined accessory template
 * @route POST /api/templates/accessories/predefined
 * @access Protected (Admin)
 */
export const createPredefinedAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const result = createPredefinedAccessoryTemplateSchema.safeParse(req.body);
    if (!result.success) {
      const errors = result.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { name, description, items = [], isPredefined } = result.data;    // Check if predefined accessory template with the same name exists
    const existingTemplate = await PredefinedAccessoryTemplate.findOne({ templateName: name });
    if (existingTemplate) {
      return sendError(res, 400, 'A predefined accessory template with this name already exists');
    }

    const newTemplate = await PredefinedAccessoryTemplate.create({
      templateName: name,
      description,
      items,
      isPredefined: isPredefined ?? true,
    });

    // Populate items for response
    const populatedTemplate = await PredefinedAccessoryTemplate.findById(newTemplate._id).populate('items');

    return sendSuccess(res, 201, { 
      message: 'Predefined accessory template created successfully', 
      template: populatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating predefined accessory template', error);
  }
};

/**
 * @description Update a predefined accessory template by ID
 * @route PUT /api/templates/accessories/predefined/:id
 * @access Protected (Admin)
 */
export const updatePredefinedAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    // Validate request body
    const bodyResult = updatePredefinedAccessoryTemplateSchema.safeParse(req.body);
    if (!bodyResult.success) {
      const errors = bodyResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const updateData = bodyResult.data;

    // Check if the template exists
    const existingTemplate = await PredefinedAccessoryTemplate.findById(id);
    if (!existingTemplate) {
      return sendError(res, 404, 'Predefined accessory template not found');
    }    // Check if name is being updated and if it's already in use
    if (updateData.name && updateData.name !== existingTemplate.templateName) {
      const nameExists = await PredefinedAccessoryTemplate.findOne({ 
        templateName: updateData.name,
        _id: { $ne: id } // Exclude the current template from the search
      });
      
      if (nameExists) {
        return sendError(res, 400, 'Template name is already in use');
      }    }

    // Prepare update data with field mapping
    const dbUpdateData: any = {};
    if (updateData.name) dbUpdateData.templateName = updateData.name;
    if (updateData.description) dbUpdateData.description = updateData.description;
    if (updateData.items) dbUpdateData.items = updateData.items;
    if (updateData.isPredefined !== undefined) dbUpdateData.isPredefined = updateData.isPredefined;

    const updatedTemplate = await PredefinedAccessoryTemplate.findByIdAndUpdate(
      id,
      { $set: dbUpdateData },
      { new: true, runValidators: true }
    ).populate('items');

    return sendSuccess(res, 200, { 
      message: 'Predefined accessory template updated successfully', 
      template: updatedTemplate 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating predefined accessory template', error);
  }
};

/**
 * @description Delete a predefined accessory template by ID
 * @route DELETE /api/templates/accessories/predefined/:id
 * @access Protected (Admin)
 */
export const deletePredefinedAccessoryTemplate = async (req: Request, res: Response) => {
  try {
    // Validate request parameters
    const paramResult = templateIdSchema.safeParse(req.params);
    if (!paramResult.success) {
      const errors = paramResult.error.issues.map(issue => issue.message);
      return sendError(res, 400, 'Validation failed', errors);
    }

    const { id } = paramResult.data;
    const template = await PredefinedAccessoryTemplate.findByIdAndDelete(id);
    
    if (!template) {
      return sendError(res, 404, 'Predefined accessory template not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Predefined accessory template deleted successfully',
      templateId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting predefined accessory template', error);
  }
};
