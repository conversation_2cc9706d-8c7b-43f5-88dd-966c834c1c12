import { z } from 'zod';

// Create accessory item schema
export const createAccessoryItemSchema = z.object({
  itemName: z.string({
    required_error: 'Item name is required',
    invalid_type_error: 'Item name must be a string',
  }).min(1, { message: 'Item name is required' })
    .trim(),
  quantity: z.number({
    required_error: 'Quantity is required',
    invalid_type_error: 'Quantity must be a number',
  }).min(1, { message: 'Quantity must be at least 1' }),
  quantityType: z.enum(['individual', 'set'], {
    required_error: 'Quantity type is required',
    invalid_type_error: 'Quantity type must be either "individual" or "set"',
  }),
  amount: z.number({
    required_error: 'Amount is required',
    invalid_type_error: 'Amount must be a number',
  }).min(0, { message: 'Amount cannot be negative' }),
});

// Update accessory item schema (all fields optional)
export const updateAccessoryItemSchema = z.object({
  itemName: z.string()
    .min(1, { message: 'Item name cannot be empty' })
    .trim()
    .optional(),
  quantity: z.number()
    .min(1, { message: 'Quantity must be at least 1' })
    .optional(),
  quantityType: z.enum(['individual', 'set'], {
    invalid_type_error: 'Quantity type must be either "individual" or "set"',
  }).optional(),
  amount: z.number()
    .min(0, { message: 'Amount cannot be negative' })
    .optional(),
});

// Accessory item ID validation schema
export const accessoryItemIdSchema = z.object({
  id: z.string({
    required_error: 'Accessory item ID is required',
    invalid_type_error: 'Accessory item ID must be a string',
  }).min(1, { message: 'Accessory item ID is required' }),
});

// Search accessory items schema
export const searchAccessoryItemSchema = z.object({
  query: z.string({
    required_error: 'Search query is required',
    invalid_type_error: 'Search query must be a string',
  }).min(1, { message: 'Search query must be at least 1 character' }),
});

// Create predefined accessory item schema (same as create but with isPredefined)
export const createPredefinedAccessoryItemSchema = createAccessoryItemSchema.extend({
  isPredefined: z.boolean().default(true).optional(),
});

// Update predefined accessory item schema
export const updatePredefinedAccessoryItemSchema = updateAccessoryItemSchema.extend({
  isPredefined: z.boolean().optional(),
});
