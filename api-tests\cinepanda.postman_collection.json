{"info": {"_postman_id": "7c8e9f2a-6d1b-4f5c-9f3a-b5e221a7d8c2", "name": "Cinepanda API Collection", "description": "Complete collection for testing all Cinepanda API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Register Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\",\n    \"role\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('token', response.token);", "    console.log('<PERSON><PERSON> saved:', response.token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('token', response.token);", "    console.log('Admin token saved:', response.token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}}}]}, {"name": "Clients", "item": [{"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}}}, {"name": "Create Client", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Client\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1234567890\",\n    \"address\": \"123 Main St, City, State 12345\"\n}"}, "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}}}, {"name": "Get Client by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}}}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Client Name\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+1987654321\",\n    \"address\": \"456 Updated St, New City, State 54321\"\n}"}, "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}}}, {"name": "Delete Client", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}}}]}, {"name": "Projects", "item": [{"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/projects", "host": ["{{baseUrl}}"], "path": ["api", "projects"]}}}, {"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Test Project\",\n    \"description\": \"A test project for validation\",\n    \"clientId\": \"{{clientId}}\",\n    \"status\": \"PLANNED\",\n    \"stage\": \"PLANNING\",\n    \"startDate\": \"2024-01-15\",\n    \"endDate\": \"2024-02-15\",\n    \"budget\": 50000\n}"}, "url": {"raw": "{{baseUrl}}/api/projects", "host": ["{{baseUrl}}"], "path": ["api", "projects"]}}}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}}}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Project Title\",\n    \"description\": \"Updated project description\",\n    \"status\": \"IN_PROGRESS\",\n    \"stage\": \"EXECUTION\",\n    \"budget\": 75000\n}"}, "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}}}, {"name": "Delete Project", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}}}]}, {"name": "Templates", "item": [{"name": "Equipment Templates", "item": [{"name": "Get All Equipment Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment"]}}}, {"name": "Create Equipment Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Camera Equipment\",\n    \"category\": \"camera\",\n    \"specifications\": {\n        \"resolution\": \"4K\",\n        \"lensMount\": \"EF\",\n        \"recordingFormat\": \"ProRes\"\n    },\n    \"pricePerDay\": 250.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment"]}}}, {"name": "Get Equipment Template by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}}}, {"name": "Update Equipment Template", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Updated Camera Equipment\",\n    \"category\": \"camera\",\n    \"specifications\": {\n        \"resolution\": \"6K\",\n        \"lensMount\": \"EF\",\n        \"recordingFormat\": \"ProRes RAW\"\n    },\n    \"pricePerDay\": 350.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}}}, {"name": "Delete Equipment Template", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}}}]}, {"name": "Service Templates", "item": [{"name": "Get All Service Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/service", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service"]}}}, {"name": "Create Service Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Video Editing Service\",\n    \"category\": \"post-production\",\n    \"description\": \"Professional video editing and color grading\",\n    \"pricePerHour\": 75.00,\n    \"estimatedDuration\": 8\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/service", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service"]}}}]}, {"name": "Accessory Templates", "item": [{"name": "Get All Accessory Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/accessory", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory"]}}}, {"name": "Create Accessory Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Tripod\",\n    \"category\": \"support\",\n    \"specifications\": {\n        \"maxHeight\": \"180cm\",\n        \"material\": \"Carbon Fiber\",\n        \"weight\": \"2.5kg\"\n    },\n    \"pricePerDay\": 25.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/accessory", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory"]}}}]}, {"name": "Installation Templates", "item": [{"name": "Get All Installation Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/templates/installation", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation"]}}}, {"name": "Create Installation Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Studio Setup\",\n    \"category\": \"studio\",\n    \"description\": \"Complete studio lighting and camera setup\",\n    \"requirements\": {\n        \"space\": \"minimum 5x5 meters\",\n        \"power\": \"220V, 32A\",\n        \"ventilation\": \"required\"\n    },\n    \"estimatedDuration\": 4,\n    \"pricePerInstallation\": 500.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/installation", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation"]}}}]}]}], "variable": [{"key": "baseUrl", "value": "localhost:8000", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "clientId", "value": "", "type": "string"}, {"key": "projectId", "value": "", "type": "string"}, {"key": "templateId", "value": "", "type": "string"}]}