import { z } from 'zod';

// Create installation item schema
export const createInstallationItemSchema = z.object({
  name: z.string({
    required_error: 'Installation item name is required',
    invalid_type_error: 'Installation item name must be a string',
  }).min(3, { message: 'Name must be at least 3 characters long' })
    .max(100, { message: 'Name cannot exceed 100 characters' })
    .trim(),
  price: z.number({
    required_error: 'Price is required',
    invalid_type_error: 'Price must be a number',
  }).min(0, { message: 'Price cannot be negative' }),
});

// Update installation item schema (all fields optional)
export const updateInstallationItemSchema = z.object({
  name: z.string()
    .min(3, { message: 'Name must be at least 3 characters long' })
    .max(100, { message: 'Name cannot exceed 100 characters' })
    .trim()
    .optional(),
  price: z.number()
    .min(0, { message: 'Price cannot be negative' })
    .optional(),
});

// Installation item ID validation schema
export const installationItemIdSchema = z.object({
  id: z.string({
    required_error: 'Installation item ID is required',
    invalid_type_error: 'Installation item ID must be a string',
  }).min(1, { message: 'Installation item ID is required' }),
});

// Search installation items schema
export const searchInstallationItemSchema = z.object({
  query: z.string({
    required_error: 'Search query is required',
    invalid_type_error: 'Search query must be a string',
  }).min(1, { message: 'Search query must be at least 1 character' }),
});

// Create predefined installation item schema (same as create but with isPredefined)
export const createPredefinedInstallationItemSchema = createInstallationItemSchema.extend({
  isPredefined: z.boolean().default(true).optional(),
});

// Update predefined installation item schema
export const updatePredefinedInstallationItemSchema = updateInstallationItemSchema.extend({
  isPredefined: z.boolean().optional(),
});
