# Cinepanda - Movie Discovery Platform

A full-stack TypeScript application for discovering and managing movies.

## Project Structure

```
cinepanda/
├── frontend/          # React + TypeScript frontend
├── backend/           # Node.js + Express + TypeScript backend
├── package.json       # Root package.json (workspace configuration)
└── tsconfig.json      # Root TypeScript configuration
```

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn

## Getting Started

### Installation

1. Clone the repository
2. Install dependencies for all packages:
   ```bash
   npm run install:all
   ```

### Development

To run both frontend and backend concurrently:
```bash
npm run dev
```

To run individually:
```bash
# Frontend only
npm run dev:frontend

# Backend only
npm run dev:backend
```

### Building

To build both frontend and backend:
```bash
npm run build
```

## Backend

- **Framework**: Express.js with TypeScript
- **Port**: 8000 (default)
- **Features**:
  - Type-safe API endpoints
  - Error handling middleware
  - Request logging
  - CORS configuration
  - Health check endpoint

### Backend Scripts

```bash
cd backend

# Development with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Clean build files
npm run clean
```

### API Endpoints

- `GET /health` - Health check
- `GET /api` - API information

## Frontend

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **Port**: 5173 (default)

### Frontend Scripts

```bash
cd frontend

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

## Environment Variables

### Backend (.env)

```env
PORT=8000
FRONTEND_URL=http://localhost:5173
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the ISC License.