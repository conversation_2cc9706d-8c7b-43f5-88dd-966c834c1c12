// API Service Layer for CinePanda Frontend
import axios, { type AxiosError, type InternalAxiosRequestConfig } from 'axios'; // Corrected type import

const API_BASE_URL = 'http://localhost:8000/api';

// Create an Axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const nonAuthRoutes = ['/auth/login', '/auth/register'];
    // Do not add token for auth routes if the config.url is defined
    if (config.url && nonAuthRoutes.some(route => config.url!.includes(route))) {
      return config;
    }
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Types
export interface Client {
  _id: string // Changed from id: number
  clientName: string
  number: string
  email?: string
  place: string
  address: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface CreateClientData {
  clientName: string
  number: string
  email?: string
  place: string
  address: string
  description?: string
}

export interface User {
  _id: string; // Changed from id: number
  username: string;
  email: string;
  role: string; // Added
  createdAt: string;
  updatedAt: string;
  __v: number; // Added
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
}

// Represents the nested 'data' object in the auth response
export interface AuthResponseData {
  user: User;
  token: string;
}

// Represents the entire API response for authentication
export interface AuthResponse {
  success: boolean;
  message: string;
  data: AuthResponseData;
  // token_type is removed as it's not in the actual response
}

// API Error Class
export class ApiError extends Error {
  status: number;
  details?: any;
  isAxiosError: boolean;

  constructor(message: string, status: number, details?: any, isAxiosError = false) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.details = details;
    this.isAxiosError = isAxiosError;
  }
}

// Helper function to handle API responses
async function handleResponse<T>(promise: Promise<any>): Promise<T> {
  try {
    const response = await promise;
    return response.data;
  } catch (error) {
    const err = error as AxiosError<any>; // Type assertion
    let errorMessage = 'An unexpected error occurred';
    let errorStatus = 500;
    let errorDetails = null;
    let isAxiosError = false;

    if (axios.isAxiosError(err)) {
      isAxiosError = true;
      errorMessage = err.response?.data?.message || err.message;
      errorStatus = err.response?.status || 500;
      errorDetails = err.response?.data?.detail || err.response?.data; // Capture more details if available
      
      // Handle FastAPI validation errors specifically if they are in err.response.data.detail
      if (err.response?.data?.detail && Array.isArray(err.response.data.detail)) {
        errorMessage = err.response.data.detail.map((e: any) => e.msg).join(', ');
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }
    throw new ApiError(errorMessage, errorStatus, errorDetails, isAxiosError);
  }
}

// Get auth token from localStorage
function getAuthToken(): string | null {
  return localStorage.getItem('authToken')
}

// Create headers with auth token - Commented out as it's largely unused with apiClient
/*
function createHeaders(includeAuth = true): Record<string, string> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // If includeAuth is true and a token exists, it would typically be added here.
  // However, for apiClient requests, the interceptor handles this.
  // This check is more for non-apiClient calls or if explicitly needing to override.
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      // headers.Authorization = `Bearer ${token}`; // Handled by interceptor for apiClient
    }
  }
  return headers;
}
*/

// Auth API
export const authApi = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return handleResponse<AuthResponse>(
      apiClient.post(`/auth/login`, credentials) // No headers needed, interceptor won't add token
    );
  },

  async register(data: RegisterData): Promise<User> {
    return handleResponse<User>(
      apiClient.post(`/auth/register`, data) // No headers needed, interceptor won't add token
    );
  },

  async getCurrentUser(): Promise<User> {
    return handleResponse<User>(apiClient.get(`/auth/me`)); // Token added by interceptor
  },
}

// Clients API
export const clientsApi = {
  async getClients(): Promise<Client[]> {
    return handleResponse<Client[]>(apiClient.get(`/clients`));
  },

  async getClient(id: string): Promise<Client> {
    return handleResponse<Client>(apiClient.get(`/clients/${id}`));
  },

  async createClient(data: CreateClientData): Promise<Client> {
    return handleResponse<Client>(apiClient.post(`/clients`, data));
  },

  async updateClient(id: string, data: Partial<CreateClientData>): Promise<Client> {
    return handleResponse<Client>(apiClient.put(`/clients/${id}`, data));
  },

  async deleteClient(id: string): Promise<void> {
    return handleResponse<void>(apiClient.delete(`/clients/${id}`));
  },
}

// Template Types
export interface Equipment {
  _id: string;
  name: string;
  description?: string;
  category?: string;
  rentalRate?: number;
  purchaseCost?: number;
  supplier?: string;
  quantityInStock?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Accessory {
  _id: string;
  name: string;
  description?: string;
  category?: string;
  cost?: number;
  supplier?: string;
  quantityInStock?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Installation {
  _id: string;
  name: string;
  description?: string;
  category?: string;
  estimatedTime?: string;
  laborCost?: number;
  materialsCost?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Service {
  _id: string;
  name: string;
  description?: string;
  category?: string;
  hourlyRate?: number;
  estimatedHours?: number;
  createdAt: string;
  updatedAt: string;
}

export type PredefinedTemplate =
  | (Equipment & { templateType: 'Equipment' })
  | (Accessory & { templateType: 'Accessory' })
  | (Installation & { templateType: 'Installation' })
  | (Service & { templateType: 'Service' });

// API Response types for templates
export interface EquipmentsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    equipments: Equipment[];
  };
}

export interface AccessoriesResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    accessories: Accessory[];
  };
}

export interface InstallationsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    installations: Installation[];
  };
}

export interface ServicesResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    services: Service[];
  };
}

// Templates API
export const templatesApi = {
  async getEquipments(): Promise<EquipmentsResponse> {
    return handleResponse<EquipmentsResponse>(
      apiClient.get(`/templates/equipments/predefined`)
    );
  },

  async getAccessories(): Promise<AccessoriesResponse> {
    return handleResponse<AccessoriesResponse>(
      apiClient.get(`/templates/accessories/predefined`)
    );
  },

  async getInstallations(): Promise<InstallationsResponse> {
    return handleResponse<InstallationsResponse>(
      apiClient.get(`/templates/installations/predefined`)
    );
  },

  async getServices(): Promise<ServicesResponse> {
    return handleResponse<ServicesResponse>(
      apiClient.get(`/templates/services/predefined`)
    );
  },
};

// Template Item Types
export interface EquipmentItem {
  _id: string;
  itemName: string;
  specification?: string;
  quantity: number;
  quantityType: 'individual' | 'set';
  price: number;
  isPredefined: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AccessoryItem {
  _id: string;
  itemName: string;
  quantity: number;
  quantityType: 'individual' | 'set';
  amount: number;
  isPredefined: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface InstallationItem {
  _id: string;
  name: string;
  price: number;
  isPredefined: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceItem {
  _id: string;
  serviceName: string;
  areaInSquareFeet: number;
  pricePerSquareFoot: number;
  totalPrice: number;
  isPredefined: boolean; // Assuming services can also be predefined items
  createdAt: string;
  updatedAt: string;
}

export type PredefinedTemplateItem =
  | (EquipmentItem & { itemType: 'Equipment' })
  | (AccessoryItem & { itemType: 'Accessory' })
  | (InstallationItem & { itemType: 'Installation' })
  | (ServiceItem & { itemType: 'Service' });

// API Response types for template items
export interface EquipmentItemsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    items: EquipmentItem[];
  };
}

export interface AccessoryItemsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    items: AccessoryItem[];
  };
}

export interface InstallationItemsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    items: InstallationItem[];
  };
}

export interface ServiceItemsResponse {
  success: boolean;
  message: string;
  data: {
    count: number;
    items: ServiceItem[];
  };
}

// Individual item creation response types (assuming backend returns the created item)
export interface CreateEquipmentItemResponse {
  success: boolean;
  message: string;
  data: EquipmentItem;
}

export interface CreateAccessoryItemResponse {
  success: boolean;
  message: string;
  data: AccessoryItem;
}

export interface CreateInstallationItemResponse {
  success: boolean;
  message: string;
  data: InstallationItem;
}

export interface CreateServiceItemResponse {
  success: boolean;
  message: string;
  data: ServiceItem;
}


// Template Items API
export const templateItemsApi = {
  async getEquipmentItems(): Promise<EquipmentItemsResponse> {
    return handleResponse<EquipmentItemsResponse>(
      apiClient.get(`/templates/items/equipments/predefined`) // Changed to fetch predefined
    );
  },
  async createEquipmentItem(data: Omit<EquipmentItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>): Promise<CreateEquipmentItemResponse> {
    return handleResponse<CreateEquipmentItemResponse>(
      apiClient.post(`/templates/items/equipments/predefined`, { ...data, isPredefined: true })
    );
  },
  async updateEquipmentItem(id: string, data: Partial<Omit<EquipmentItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>): Promise<EquipmentItem> {
    return handleResponse<EquipmentItem>(
      apiClient.put(`/templates/items/equipments/predefined/${id}`, { ...data, isPredefined: true })
    );
  },
  async deleteEquipmentItem(id: string): Promise<void> {
    return handleResponse<void>(apiClient.delete(`/templates/items/equipments/predefined/${id}`));
  },

  async getAccessoryItems(): Promise<AccessoryItemsResponse> {
    return handleResponse<AccessoryItemsResponse>(
      apiClient.get(`/templates/items/accessories/predefined`) // Changed to fetch predefined
    );
  },
  async createAccessoryItem(data: Omit<AccessoryItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>): Promise<CreateAccessoryItemResponse> {
    return handleResponse<CreateAccessoryItemResponse>(
      apiClient.post(`/templates/items/accessories/predefined`, { ...data, isPredefined: true })
    );
  },
  async updateAccessoryItem(id: string, data: Partial<Omit<AccessoryItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>): Promise<AccessoryItem> {
    return handleResponse<AccessoryItem>(
      apiClient.put(`/templates/items/accessories/predefined/${id}`, { ...data, isPredefined: true })
    );
  },
  async deleteAccessoryItem(id: string): Promise<void> {
    return handleResponse<void>(apiClient.delete(`/templates/items/accessories/predefined/${id}`));
  },

  async getInstallationItems(): Promise<InstallationItemsResponse> {
    return handleResponse<InstallationItemsResponse>(
      apiClient.get(`/templates/items/installations/predefined`) // Changed to fetch predefined
    );
  },
  async createInstallationItem(data: Omit<InstallationItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>): Promise<CreateInstallationItemResponse> {
    return handleResponse<CreateInstallationItemResponse>(
      apiClient.post(`/templates/items/installations/predefined`, { ...data, isPredefined: true })
    );
  },
  async updateInstallationItem(id: string, data: Partial<Omit<InstallationItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>): Promise<InstallationItem> {
    return handleResponse<InstallationItem>(
      apiClient.put(`/templates/items/installations/predefined/${id}`, { ...data, isPredefined: true })
    );
  },
  async deleteInstallationItem(id: string): Promise<void> {
    return handleResponse<void>(apiClient.delete(`/templates/items/installations/predefined/${id}`));
  },

  async getServiceItems(): Promise<ServiceItemsResponse> {
    return handleResponse<ServiceItemsResponse>(
      apiClient.get(`/templates/items/services/predefined`) // Changed to fetch predefined
    );
  },
  async createServiceItem(data: Omit<ServiceItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>): Promise<CreateServiceItemResponse> {
    return handleResponse<CreateServiceItemResponse>(
      apiClient.post(`/templates/items/services/predefined`, { ...data, isPredefined: true })
    );
  },
  async updateServiceItem(id: string, data: Partial<Omit<ServiceItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>): Promise<ServiceItem> {
    return handleResponse<ServiceItem>(
      apiClient.put(`/templates/items/services/predefined/${id}`, { ...data, isPredefined: true })
    );
  },
  async deleteServiceItem(id: string): Promise<void> {
    return handleResponse<void>(apiClient.delete(`/templates/items/services/predefined/${id}`));
  },
};

// Template creation types
export interface CreateTemplateData {
  name: string;
  description?: string;
  items: string[]; // Array of item IDs
}

export interface CreateTemplateResponse {
  success: boolean;
  message: string;
  template: {
    _id: string;
    templateName: string;
    description?: string;
    items: any[];
    totalAmount: number;
    isPredefined: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

// Template creation APIs
export const templatesCreationApi = {
  async createEquipmentTemplate(data: CreateTemplateData): Promise<CreateTemplateResponse> {
    return handleResponse<CreateTemplateResponse>(
      apiClient.post('/templates/equipments/predefined', {
        name: data.name,
        description: data.description,
        items: data.items,
        isPredefined: true
      })
    );
  },

  async createAccessoryTemplate(data: CreateTemplateData): Promise<CreateTemplateResponse> {
    return handleResponse<CreateTemplateResponse>(
      apiClient.post('/templates/accessories/predefined', {
        name: data.name,
        description: data.description,
        items: data.items,
        isPredefined: true
      })
    );
  },

  async createInstallationTemplate(data: CreateTemplateData): Promise<CreateTemplateResponse> {
    return handleResponse<CreateTemplateResponse>(
      apiClient.post('/templates/installations/predefined', {
        name: data.name,
        description: data.description,
        items: data.items,
        isPredefined: true
      })
    );
  },

  async createServiceTemplate(data: CreateTemplateData): Promise<CreateTemplateResponse> {
    return handleResponse<CreateTemplateResponse>(
      apiClient.post('/templates/services/predefined', {
        name: data.name,
        description: data.description,
        items: data.items,
        isPredefined: true
      })
    );
  },
};