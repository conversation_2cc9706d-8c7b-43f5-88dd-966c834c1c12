import { Router } from 'express';
import { 
  getAllProjects, 
  getProjectById, 
  createProject, 
  updateProject,
  deleteProject,
  updateProjectStatus
} from '../controllers/Projects/projects.controller';
import { protect, restrictTo } from '../middleware/auth.middleware';

const router = Router();

// All project routes are protected
router.use(protect);

// Routes restricted to admin and manager roles
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllProjects)
  .post(restrictTo('admin', 'manager'), createProject);

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getProjectById)
  .put(restrictTo('admin', 'manager'), updateProject)
  .delete(restrictTo('admin'), deleteProject); // Only admin can delete

// Route for updating project status
router.patch('/:id/status', restrictTo('admin', 'manager'), updateProjectStatus);

export default router;
