import { Router, Request, Response } from 'express';
import { sendResponse } from '../middleware';
import authRoutes from './auth.routes';
import projectsRoutes from './projects.routes';
import clientsRoutes from './clients.routes';
import templatesRoutes from './templates.routes';

const router = Router();

// Basic API info route
router.get('/', (req: Request, res: Response) => {
  sendResponse(res, 200, {
    message: 'Welcome to Cinepanda API',
    version: '1.0.0',
    endpoints: [      'GET /api - API information',
      'GET /health - Health check',
      'POST /api/auth/register - Register a new user',
      'POST /api/auth/login - Login user',
      'GET /api/auth/me - Get current user (protected)',      'GET /api/projects - Get all projects (protected)',
      'GET /api/projects/:id - Get project by ID (protected)',
      'POST /api/projects - Create new project (protected)',
      'PUT /api/projects/:id - Update project (protected)',
      'DELETE /api/projects/:id - Delete project (protected)',
      'PATCH /api/projects/:id/status - Update project status (protected)',      'GET /api/clients - Get all clients (protected)',
      'GET /api/clients/:id - Get client by ID (protected)',
      'POST /api/clients - Create new client (protected)',
      'PUT /api/clients/:id - Update client (protected)',
      'DELETE /api/clients/:id - Delete client (protected)',
      'GET /api/clients/search - Search clients (protected)',
      'GET /api/templates/equipment - Get all equipment templates (protected)',
      'GET /api/templates/equipment/:id - Get equipment template by ID (protected)',
      'POST /api/templates/equipment - Create new equipment template (protected)',
      'PUT /api/templates/equipment/:id - Update equipment template (protected)',
      'DELETE /api/templates/equipment/:id - Delete equipment template (protected)',
      'GET /api/templates/equipment/predefined - Get all predefined equipment templates (protected)',
    ],
  });
});

// Mount auth routes
router.use('/auth', authRoutes);

// Mount projects routes
router.use('/projects', projectsRoutes);

// Mount clients routes
router.use('/clients', clientsRoutes);

// Mount templates routes
router.use('/templates', templatesRoutes);

export default router;
