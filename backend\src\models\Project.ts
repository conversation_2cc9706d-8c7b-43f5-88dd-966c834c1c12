import { Schema, model, Document, Types } from 'mongoose';
import { IClient } from './Client';
import { IPhase } from './Phase';

// Define an enum for Project status
export enum ProjectStatus {
  PENDING = 'Pending',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  ON_HOLD = 'On Hold',
  CANCELLED = 'Cancelled',
}

// Define an enum for Project stage
export enum ProjectStage {
  QUOTATION = 'quotation',
  PROJECT = 'project',
}

// Interface for the Project document
export interface IProject extends Document {
  client: Types.ObjectId | IClient; // Reference to Client
  phases: Types.ObjectId[] | IPhase[]; // Array of Phase references
  projectName: string;
  projectCost: number; // Calculated based on the sum of costs from all phases
  projectDescription?: string;
  followupCallDate?: Date;
  startDate?: Date;
  endDate?: Date;
  amountReceived: number;
  status: ProjectStatus;
  stage: ProjectStage;
  createdAt: Date;
  updatedAt: Date;
}

// Mongoose schema for Project
const ProjectSchema = new Schema<IProject>(
  {
    client: {
      type: Schema.Types.ObjectId,
      ref: 'Client',
      required: [true, 'Client is required.'],
    },
    phases: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Phase',
      },
    ],
    projectName: {
      type: String,
      required: [true, 'Project name is required.'],
      trim: true,
      minlength: [3, 'Project name must be at least 3 characters long.'],
      maxlength: [100, 'Project name cannot exceed 100 characters.'],
    },
    projectCost: {
      type: Number,
      required: true,
      default: 0,
      min: [0, 'Project cost cannot be negative.'],
      // TODO: Implement logic to calculate this based on the sum of phaseCosts from referenced phases
    },
    projectDescription: {
      type: String,
      trim: true,
      maxlength: [1000, 'Project description cannot exceed 1000 characters.'],
    },
    followupCallDate: {
      type: Date,
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
      validate: [
        function (this: IProject, value: Date | undefined): boolean {
          if (this.startDate && value) {
            return value >= this.startDate;
          }
          return true; // Allow if startDate is not set or endDate is not set
        },
        'End date cannot be before start date.',
      ],
    },
    amountReceived: {
      type: Number,
      required: true,
      default: 0,
      min: [0, 'Amount received cannot be negative.'],
    },
    status: {
      type: String,
      enum: Object.values(ProjectStatus),
      default: ProjectStatus.PENDING,
      required: [true, 'Project status is required.'],
    },
    stage: {
      type: String,
      enum: Object.values(ProjectStage),
      default: ProjectStage.QUOTATION,
      required: [true, 'Project stage is required.'],
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    // TODO: Add virtuals or pre-save hooks for calculated fields like projectCost if desired.
  }
);

// Optional pre-save hook for date calculations or validations
ProjectSchema.pre<IProject>('save', function (next) {
  // Add any pre-save logic here if needed
  // For example, calculate project duration or validate business rules
  next();
});

// TODO: Add a pre-save or pre-validate hook to calculate projectCost by summing up
// phaseCost from all referenced phases. This will require populating these fields first before saving.

// Create and export the Project model
const Project = model<IProject>('Project', ProjectSchema);

export default Project;