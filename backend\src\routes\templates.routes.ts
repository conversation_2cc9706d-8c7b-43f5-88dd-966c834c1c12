import { Router } from 'express';
import { protect } from '../middleware/auth.middleware';
import equipmentRoutes from './templateRoutes/equipment.routes';
import serviceRoutes from './templateRoutes/service.routes';
import accessoryRoutes from './templateRoutes/accessory.routes';
import installationRoutes from './templateRoutes/installation.routes';
import accessoryItemRoutes from './templateRoutes/items/accessoryItem.routes';
import equipmentItemRoutes from './templateRoutes/items/equipmentItem.routes';
import installationItemRoutes from './templateRoutes/items/installationItem.routes';
import serviceItemRoutes from './templateRoutes/items/serviceItem.routes';

const router = Router();

// All template routes are protected
router.use(protect);

// Mount the specific template routers
router.use('/equipments', equipmentRoutes);
router.use('/services', serviceRoutes);
router.use('/accessories', accessoryRoutes);
router.use('/installations', installationRoutes);

// Mount the specific item routers
router.use('/items/accessories', accessoryItemRoutes);
router.use('/items/equipments', equipmentItemRoutes);
router.use('/items/installations', installationItemRoutes);
router.use('/items/services', serviceItemRoutes);

export default router;
