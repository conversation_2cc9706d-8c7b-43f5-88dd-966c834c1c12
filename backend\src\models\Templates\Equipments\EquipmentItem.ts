import mongoose, { Schema, Document } from 'mongoose';

// Define an interface representing a document in MongoDB.
export interface IEquipmentItem extends Document {
  itemName: string;
  specification?: string; // Optional field
  quantity: number;
  quantityType: 'individual' | 'set';
  price: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const EquipmentItemSchema: Schema<IEquipmentItem> = new Schema(
  {
    itemName: {
      type: String,
      required: [true, 'Item name is required'],
      trim: true,
    },
    specification: {
      type: String,
      trim: true,
      default: null, // Explicitly set default to null for optional string
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [1, 'Quantity must be at least 1'],
    },
    quantityType: {
      type: String,
      enum: ['individual', 'set'],
      required: [true, 'Quantity type is required'],
      default: 'individual',
    },
    price: {
      type: Number,
      required: [true, 'Price is required'],
      min: [0, 'Price cannot be negative'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Create and export the Mongoose model.
const EquipmentItem = mongoose.model<IEquipmentItem>(
  'EquipmentItem',
  EquipmentItemSchema
);

export default EquipmentItem;
