import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

interface PublicRouteProps {
  children: React.ReactNode
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth()
  const location = useLocation()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--color-deep-blue)] mx-auto mb-4"></div>
          <p className="text-[var(--color-steel-blue)]">Loading...</p>
        </div>
      </div>
    )
  }

  // Only redirect if fully authenticated (not during login process)
  if (isAuthenticated) {
    // Check if there's a redirect location from ProtectedRoute
    const from = location.state?.from?.pathname || '/dashboard'
    return <Navigate to={from} replace />
  }

  return <>{children}</>
}

export default PublicRoute
