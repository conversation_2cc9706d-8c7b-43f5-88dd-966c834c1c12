import mongoose, { Schema, Document, Types } from 'mongoose';
import { IAccessoryItem } from './AccessoryItem'; // Assuming this path is correct, will be AccessoryItem.ts if file is renamed

// Define an interface representing a document in MongoDB.
export interface IAccessoryTemplate extends Document {
  templateName: string;
  items: Types.ObjectId[] | IAccessoryItem[]; // Can be populated or just ObjectIds
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const AccessoryTemplateSchema: Schema<IAccessoryTemplate> = new Schema(
  {
    templateName: {
      type: String,
      required: [true, 'Template name is required'],
      trim: true,
    },
    items: [
      {
        type: Schema.Types.ObjectId,
        ref: 'AccessoryItem', // Reference to the AccessoryItem model
      },
    ],
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required'],
      default: 0,
      min: [0, 'Total amount cannot be negative'],
    },
     isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
    // Consider adding a pre-save hook to calculate totalAmount if needed
    // or calculate it on the fly when querying.
  }
);

// Optional: Pre-save hook to calculate totalAmount from items if they are embedded or if you fetch them first.
// This example assumes you might calculate it elsewhere or store it directly.
// If items are just ObjectIds, you'd need to populate them to calculate the sum here.
// AccessoryTemplateSchema.pre('save', async function (next) {
//   if (this.isModified('items') || this.isNew) {
//     // If items are populated (full documents)
//     // this.totalAmount = this.items.reduce((acc, currentItem) => {
//     //   if (typeof currentItem !== 'string' && currentItem.amount) { // Check if populated
//     //     return acc + currentItem.amount;
//     //   }
//     //   return acc;
//     // }, 0);

//     // If items are ObjectIds, you would need to fetch them:
//     // const populatedItems = await mongoose.model('AccessoryItem').find({ '_id': { $in: this.items } });
//     // this.totalAmount = populatedItems.reduce((acc, currentItem) => acc + currentItem.amount, 0);
//   }
//   next();
// });

// Create and export the Mongoose model.
const AccessoryTemplate = mongoose.model<IAccessoryTemplate>(
  'AccessoryTemplate',
  AccessoryTemplateSchema
);

export default AccessoryTemplate;
