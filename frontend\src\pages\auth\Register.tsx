import { useState } from 'react'
import { Eye, EyeOff, Mail, Lock, User, UserPlus } from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import { authApi, ApiError } from '../../services/api'

const Register = () => {
  const navigate = useNavigate()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [loading, setLoading] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match')
      return
    }

    if (formData.password.length < 6) {
      toast.error('Password must be at least 6 characters')
      return
    }

    try {
      setLoading(true)
      
      // Call the real API
      await authApi.register({
        username: formData.username,
        email: formData.email,
        password: formData.password
      })
      
      toast.success('Account created successfully! Please sign in.')
      // Redirect to login page after successful registration
      navigate('/auth/login')
    } catch (error) {
      console.error('Registration failed:', error)
      if (error instanceof ApiError) {
        if (error.status === 422) {
          toast.error('Please check your input and try again')
        } else if (error.status === 409) {
          toast.error('Username or email already exists')
        } else {
          toast.error(`Registration failed: ${error.message}`)
        }
      } else {
        toast.error('Registration failed. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] px-4">
      <div className="max-w-md w-full space-y-8">
        {/* Header Section */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-[var(--color-deep-blue)] flex items-center justify-center mb-4">
            <UserPlus className="h-6 w-6 text-[var(--color-white)]" />
          </div>          <h2 className="text-3xl font-bold text-[var(--color-dark-indigo)]">
            Create Account
          </h2>
        </div>

        {/* Registration Form */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-xl border border-[var(--color-light-grey-blue)] p-8">
          <form className="space-y-6" onSubmit={handleSubmit}>            {/* Username Field */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Username
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  autoComplete="username"
                  required
                  value={formData.username}
                  onChange={handleChange}
                  className="w-full pl-10 pr-3 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Enter your username"
                />
              </div>
            </div>

            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-3 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full pl-10 pr-10 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  ) : (
                    <Eye className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  )}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="w-full pl-10 pr-10 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  ) : (
                    <Eye className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  )}
                </button>
              </div>            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-[var(--color-white)] bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--color-deep-blue)] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating Account...' : 'Create Account'}
              </button>
            </div>

            {/* Sign In Link */}
            <div className="text-center">
              <p className="text-sm text-[var(--color-steel-blue)]">
                Already have an account?{' '}
                <Link
                  to="/auth/login"
                  className="font-medium text-[var(--color-deep-blue)] hover:text-[var(--color-dark-indigo)] transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default Register