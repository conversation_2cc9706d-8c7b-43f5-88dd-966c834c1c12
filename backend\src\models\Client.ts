import { Schema, model, Document } from 'mongoose';

// Interface for the Client document
export interface IClient extends Document {
  clientName: string;
  number: string;
  email?: string;
  place: string;
  address: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Mongoose schema for Client
const ClientSchema = new Schema<IClient>(
  {
    clientName: {
      type: String,
      required: [true, 'Client name is required.'],
      trim: true,
      minlength: [2, 'Client name must be at least 2 characters long.'],
      maxlength: [100, 'Client name cannot exceed 100 characters.'],
    },
    number: {
      type: String,
      required: [true, 'Contact number is required.'],
      trim: true,
      // Consider adding a regex for phone number validation if specific formats are needed
      // unique: true, // Uncomment if phone numbers should be unique
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      unique: true,
      sparse: true, // Allows multiple documents to have no email, but if an email exists, it must be unique
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please fill a valid email address',
      ],
    },
    place: {
      type: String,
      required: [true, 'Place is required.'],
      trim: true,
      maxlength: [100, 'Place cannot exceed 100 characters.'],
    },
    address: {
      type: String,
      required: [true, 'Address is required.'],
      trim: true,
      maxlength: [255, 'Address cannot exceed 255 characters.'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters.'],
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Create and export the Client model
const Client = model<IClient>('Client', ClientSchema);

export default Client;
