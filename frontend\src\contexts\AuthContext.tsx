import React, { createContext, useContext, useState, useEffect } from 'react'
import type { ReactNode } from 'react'
import { authApi, ApiError } from '../services/api'
import type { User } from '../services/api'

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  loading: boolean
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  useEffect(() => {
    // Check if user is logged in on app start
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('authToken')
        if (token) {
          // Validate token with backend
          const userData = await authApi.getCurrentUser()
          setUser(userData)
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        localStorage.removeItem('authToken')
        localStorage.removeItem('userData')      
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])
  
  const login = async (email: string, password: string) => {
    try {
      setLoading(true)
      console.log('AuthContext: Attempting login with email:', email);
      
      const response = await authApi.login({ email, password });
      console.log('AuthContext: Login API response received:', response);

      if (!response || !response.success || !response.data) {
        console.error('AuthContext: Login API response is invalid or indicates failure.', response);
        throw new Error(response?.message || 'Login failed: Invalid API response structure.');
      }

      const { user, token } = response.data;

      if (!token) {
        console.error('AuthContext: Login API response missing token.', response.data);
        throw new Error('Login failed: Missing token in API response.');
      }
      if (!user) {
        console.error('AuthContext: Login API response missing user data.', response.data);
        throw new Error('Login failed: Missing user data in API response.');
      }

      console.log('AuthContext: token found:', token);
      localStorage.setItem('authToken', token);
      console.log('AuthContext: authToken stored in localStorage.');

      try {
        const userDataString = JSON.stringify(user);
        console.log('AuthContext: user data stringified:', userDataString);
        localStorage.setItem('userData', userDataString);
        console.log('AuthContext: userData stored in localStorage.');
      } catch (stringifyError) {
        console.error('AuthContext: Failed to stringify user data:', stringifyError, user);
        throw new Error('Login failed: Could not process user data.');
      }
      
      setUser(user);
      
      // Brief delay to prevent race conditions
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) { // This will now catch errors from the checks above too
      console.error('AuthContext: Login failed:', error); 
      // Preserve existing error handling logic, but ensure the new specific errors are also re-thrown
      if (error instanceof ApiError) {
        if (error.status === 401) throw new Error('Invalid email or password');
        if (error.status === 422) throw new Error('Please check your input');
        throw new Error(`Login failed: ${error.message}`);
      } else if (error instanceof Error) { // This will catch the custom errors thrown above
        throw error; // Re-throw the specific error message
      } else {
        throw new Error('Login failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }

  const logout = () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('userData')
    setUser(null)
  }

  const value: AuthContextType = {
    user,
    login,
    logout,
    loading,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
