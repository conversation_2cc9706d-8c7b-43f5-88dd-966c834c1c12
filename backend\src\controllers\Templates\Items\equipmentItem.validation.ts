import { z } from 'zod';

// Create equipment item schema
export const createEquipmentItemSchema = z.object({
  itemName: z.string({
    required_error: 'Item name is required',
    invalid_type_error: 'Item name must be a string',
  }).min(1, { message: 'Item name is required' })
    .trim(),
  specification: z.string()
    .trim()
    .optional(),
  quantity: z.number({
    required_error: 'Quantity is required',
    invalid_type_error: 'Quantity must be a number',
  }).min(1, { message: 'Quantity must be at least 1' }),
  quantityType: z.enum(['individual', 'set'], {
    required_error: 'Quantity type is required',
    invalid_type_error: 'Quantity type must be either "individual" or "set"',
  }),
  price: z.number({
    required_error: 'Price is required',
    invalid_type_error: 'Price must be a number',
  }).min(0, { message: 'Price cannot be negative' }),
});

// Update equipment item schema (all fields optional)
export const updateEquipmentItemSchema = z.object({
  itemName: z.string()
    .min(1, { message: 'Item name cannot be empty' })
    .trim()
    .optional(),
  specification: z.string()
    .trim()
    .optional(),
  quantity: z.number()
    .min(1, { message: 'Quantity must be at least 1' })
    .optional(),
  quantityType: z.enum(['individual', 'set'], {
    invalid_type_error: 'Quantity type must be either "individual" or "set"',
  }).optional(),
  price: z.number()
    .min(0, { message: 'Price cannot be negative' })
    .optional(),
});

// Equipment item ID validation schema
export const equipmentItemIdSchema = z.object({
  id: z.string({
    required_error: 'Equipment item ID is required',
    invalid_type_error: 'Equipment item ID must be a string',
  }).min(1, { message: 'Equipment item ID is required' }),
});

// Search equipment items schema
export const searchEquipmentItemSchema = z.object({
  query: z.string({
    required_error: 'Search query is required',
    invalid_type_error: 'Search query must be a string',
  }).min(1, { message: 'Search query must be at least 1 character' }),
});

// Create predefined equipment item schema (same as create but with isPredefined)
export const createPredefinedEquipmentItemSchema = createEquipmentItemSchema.extend({
  isPredefined: z.boolean().default(true).optional(),
});

// Update predefined equipment item schema
export const updatePredefinedEquipmentItemSchema = updateEquipmentItemSchema.extend({
  isPredefined: z.boolean().optional(),
});
