import { Router } from 'express';
import {
  getAllAccessoryTemplates,
  getAccessoryTemplateById,
  createAccessoryTemplate,
  updateAccessoryTemplate,
  deleteAccessoryTemplate,
  getAllPredefinedAccessoryTemplates,
  getPredefinedAccessoryTemplateById,
  createPredefinedAccessoryTemplate,
  updatePredefinedAccessoryTemplate,
  deletePredefinedAccessoryTemplate
} from '../../controllers/Templates/accessory.controller';
import { restrictTo } from '../../middleware/auth.middleware';

const router = Router();

// Accessory templates routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllAccessoryTemplates)
  .post(restrictTo('admin', 'manager'), createAccessoryTemplate);


router.route('/:id')
  .get(restrictTo('admin', 'manager'), getAccessoryTemplateById)
  .put(restrictTo('admin', 'manager'), updateAccessoryTemplate)
  .delete(restrictTo('admin'), deleteAccessoryTemplate);

export default router;
