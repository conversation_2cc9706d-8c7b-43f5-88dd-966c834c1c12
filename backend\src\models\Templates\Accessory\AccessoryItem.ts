import mongoose, { Schema, Document } from 'mongoose';

// Define an interface representing a document in MongoDB.
export interface IAccessoryItem extends Document {
  itemName: string;
  quantity: number;
  quantityType: 'individual' | 'set';
  amount: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const AccessoryItemSchema: Schema<IAccessoryItem> = new Schema(
  {
    itemName: {
      type: String,
      required: [true, 'Item name is required'],
      trim: true,
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [1, 'Quantity must be at least 1'],
    },
    quantityType: {
      type: String,
      enum: ['individual', 'set'],
      required: [true, 'Quantity type is required'],
      default: 'individual',
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0, 'Amount cannot be negative'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Create and export the Mongoose model.
const AccessoryItem = mongoose.model<IAccessoryItem>(
  'AccessoryItem',
  AccessoryItemSchema
);

export default AccessoryItem;
