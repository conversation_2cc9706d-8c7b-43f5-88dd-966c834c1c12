import { Users, FolderOpen, BarChart3, Calendar, Bell } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

const Dashboard = () => {
  const { user } = useAuth()

  const stats = [
    { title: 'Active Projects', value: '12', icon: FolderOpen, color: 'bg-[var(--color-deep-blue)]' },
    { title: 'Total Clients', value: '28', icon: Users, color: 'bg-[var(--color-dark-indigo)]' },
    { title: 'Completed Tasks', value: '156', icon: BarChart3, color: 'bg-[var(--color-steel-blue)]' },
    { title: 'Upcoming Deadlines', value: '5', icon: Calendar, color: 'bg-[var(--color-muted-blue-grey)]' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">
            Welcome back, {user?.username}! 👋
          </h1>
          <p className="text-[var(--color-steel-blue)] mt-2">
            Here's what's happening with your projects today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6">
              <div className="flex items-center">
                <div className={`${stat.color} rounded-lg p-3`}>
                  <stat.icon className="h-6 w-6 text-[var(--color-white)]" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-[var(--color-steel-blue)]">{stat.title}</p>
                  <p className="text-2xl font-bold text-[var(--color-dark-indigo)]">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Projects */}
          <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6">
            <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)] mb-4">Recent Projects</h3>
            <div className="space-y-4">
              {[
                { name: 'Wedding Video - Sarah & John', status: 'In Progress', progress: 75 },
                { name: 'Corporate Event - TechCorp', status: 'Review', progress: 90 },
                { name: 'Music Video - Local Band', status: 'Editing', progress: 60 }
              ].map((project, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-[var(--color-light-grey-blue)] rounded-lg">
                  <div>
                    <p className="font-medium text-[var(--color-dark-indigo)]">{project.name}</p>
                    <p className="text-sm text-[var(--color-steel-blue)]">{project.status}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-[var(--color-deep-blue)]">{project.progress}%</p>
                    <div className="w-16 bg-[var(--color-muted-blue-grey)] rounded-full h-2 mt-1">
                      <div 
                        className="bg-[var(--color-deep-blue)] h-2 rounded-full transition-all duration-300"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6">
            <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)] mb-4">Recent Notifications</h3>
            <div className="space-y-4">
              {[
                { message: 'New client inquiry received', time: '2 hours ago', type: 'info' },
                { message: 'Project deadline approaching', time: '4 hours ago', type: 'warning' },
                { message: 'Payment received from client', time: '1 day ago', type: 'success' }
              ].map((notification, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-[var(--color-light-grey-blue)] rounded-lg">
                  <Bell className="h-5 w-5 text-[var(--color-steel-blue)] mt-0.5" />
                  <div>
                    <p className="text-sm text-[var(--color-dark-indigo)]">{notification.message}</p>
                    <p className="text-xs text-[var(--color-muted-blue-grey)] mt-1">{notification.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
