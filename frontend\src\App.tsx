import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from './contexts/AuthContext'

// Components
import ProtectedRoute from './components/ProtectedRoute'
import PublicRoute from './components/PublicRoute'
import Layout from './components/Layout'

// Pages
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'
import Dashboard from './pages/Dashboard'
import Projects from './pages/projects/Projects'
import Clients from './pages/clients/Clients'
import AddClient from './pages/clients/AddClient'
import Settings from './pages/Settings'
import PredefinedTemplates from './pages/templates/PredefinedTemplates'
import PredefinedTemplateItems from './pages/templates/items/PredefinedTemplateItems'
import AddTemplate from './pages/templates/addTemplate/AddTemplate'

const App = () => {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public Routes */}
            <Route path="/auth/login" element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            } />
            <Route path="/auth/register" element={
              <PublicRoute>
                <Register />
              </PublicRoute>
            } />

            {/* Protected Routes with Layout */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="projects" element={<Projects />} />
              <Route path="clients" element={<Clients />} />
              <Route path="clients/add" element={<AddClient />} />
              <Route path="settings" element={<Settings />} />
              <Route path="predefined" element={<PredefinedTemplates />} />
              <Route path="templates/add" element={<AddTemplate />} />
              <Route path="templates/items/add" element={<PredefinedTemplateItems />} />
            </Route>

            {/* Catch all route - redirect unknown paths to dashboard for authenticated users */}
            <Route path="*" element={
              <ProtectedRoute>
                <Navigate to="/dashboard" replace />
              </ProtectedRoute>
            } />
          </Routes>

          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--color-white)',
                color: 'var(--color-dark-indigo)',
                border: '1px solid var(--color-light-grey-blue)',
                borderRadius: '8px',
              },
              success: {
                iconTheme: {
                  primary: 'var(--color-deep-blue)',
                  secondary: 'var(--color-white)',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: 'var(--color-white)',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App