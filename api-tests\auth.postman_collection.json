{"info": {"_postman_id": "b4f1f1a0-5e1b-4a0f-8b7a-55430c1b2f4d", "name": "Cinepanda API", "description": "Collection for testing Cinepanda API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication API endpoints for user registration and login", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"user\"\n}"}, "url": {"raw": "http://localhost:8000/api/auth/register", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api", "auth", "register"]}, "description": "Register a new user with the system"}, "response": []}, {"name": "Register Admin User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\",\n    \"role\": \"admin\"\n}"}, "url": {"raw": "http://localhost:8000/api/auth/register", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api", "auth", "register"]}, "description": "Register an admin user with the system"}, "response": []}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "http://localhost:8000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api", "auth", "login"]}, "description": "Login with a registered user"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\"\n}"}, "url": {"raw": "http://localhost:8000/api/auth/login", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api", "auth", "login"]}, "description": "Login with an admin user"}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "http://localhost:8000/api/auth/me", "protocol": "http", "host": ["localhost"], "port": "8000", "path": ["api", "auth", "me"]}, "description": "Get the currently authenticated user's details"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "token", "value": "", "type": "string", "description": "JWT token received after login"}]}