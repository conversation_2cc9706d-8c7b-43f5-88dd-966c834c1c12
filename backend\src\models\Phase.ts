import { Schema, model, Document, Types } from 'mongoose';

// Define an enum for Phase status
export enum PhaseStatus {
  PENDING = 'Pending',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
  ON_HOLD = 'On Hold',
  CANCELLED = 'Cancelled',
}

// Interface for the Phase document
export interface IPhase extends Document {
  phaseName: string;
  description?: string;
  equipmentTemplates: Types.ObjectId[]; // References to EquipmentTemplate
  serviceTemplates: Types.ObjectId[]; // References to ServiceTemplate
  accessoryOrInstallationTemplates: Types.ObjectId[]; // References to AccessoryOrInstallationTemplate
  installationTemplates: Types.ObjectId[]; // References to InstallationTemplate
  // TODO: Add other template types here in the future as needed
  phaseCost: number; // Calculated based on the sum of costs from all included templates
  startDate?: Date;
  endDate?: Date;
  estimatedDurationInDays?: number; // Can be calculated from startDate and endDate or set manually
  receivedAmount: number;
  status: PhaseStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Mongoose schema for Phase
const PhaseSchema = new Schema<IPhase>(
  {
    phaseName: {
      type: String,
      required: [true, 'Phase name is required.'],
      trim: true,
      minlength: [3, 'Phase name must be at least 3 characters long.'],
      maxlength: [100, 'Phase name cannot exceed 100 characters.'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters.'],
    },
    equipmentTemplates: [
      {
        type: Schema.Types.ObjectId,
        ref: 'EquipmentTemplate',
      },
    ],
    serviceTemplates: [
      {
        type: Schema.Types.ObjectId,
        ref: 'ServiceTemplate',
      },
    ],
    accessoryOrInstallationTemplates: [
      {
        type: Schema.Types.ObjectId,
        ref: 'AccessoryOrInstallationTemplate',
      },
    ],
    installationTemplates: [
      {
        type: Schema.Types.ObjectId,
        ref: 'InstallationTemplate',
      },
    ],
    phaseCost: {
      type: Number,
      required: true,
      default: 0,
      min: [0, 'Phase cost cannot be negative.'],
      // TODO: Implement logic to calculate this based on the sum of totalAmounts from referenced templates (e.g., using a pre-save hook or virtual)
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
      validate: [
        function (this: IPhase, value: Date | undefined): boolean {
          if (this.startDate && value) {
            return value >= this.startDate;
          }
          return true; // Allow if startDate is not set or endDate is not set
        },
        'End date cannot be before start date.',
      ],
    },
    estimatedDurationInDays: {
      type: Number,
      min: [0, 'Estimated duration cannot be negative.'],
      // TODO: Consider calculating this automatically if startDate and endDate are present, or allow manual override.
    },
    receivedAmount: {
      type: Number,
      required: true,
      default: 0,
      min: [0, 'Received amount cannot be negative.'],
    },
    status: {
      type: String,
      enum: Object.values(PhaseStatus),
      default: PhaseStatus.PENDING,
      required: [true, 'Phase status is required.'],
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    // TODO: Add virtuals or pre-save hooks for calculated fields like phaseCost and estimatedDurationInDays if desired.
  }
);

// Example pre-save hook for estimatedDurationInDays (if desired)
PhaseSchema.pre<IPhase>('save', function (next) {
  if (this.startDate && this.endDate && (this.isModified('startDate') || this.isModified('endDate'))) {
    const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
    this.estimatedDurationInDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } else if (this.startDate && !this.endDate && this.estimatedDurationInDays && (this.isModified('startDate') || this.isModified('estimatedDurationInDays'))) {
    const newEndDate = new Date(this.startDate);
    newEndDate.setDate(this.startDate.getDate() + this.estimatedDurationInDays);
    this.endDate = newEndDate;
  } else if (!this.startDate && this.endDate && this.estimatedDurationInDays && (this.isModified('endDate') || this.isModified('estimatedDurationInDays'))) {
    const newStartDate = new Date(this.endDate);
    newStartDate.setDate(this.endDate.getDate() - this.estimatedDurationInDays);
    this.startDate = newStartDate;
  }
  next();
});

// TODO: Add a pre-save or pre-validate hook to calculate phaseCost by summing up
// totalAmount from all referenced templates (EquipmentTemplate, ServiceTemplate, AccessoryOrInstallationTemplate).
// This will require populating these fields first before saving.

// Create and export the Phase model
const Phase = model<IPhase>('Phase', PhaseSchema);

export default Phase;
