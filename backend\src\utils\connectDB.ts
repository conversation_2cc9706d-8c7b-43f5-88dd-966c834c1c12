import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI;

const connectDB = async (): Promise<void> => {
  if (!MONGODB_URI) {
    // Throw an error if MONGODB_URI is not defined
    throw new Error('Error: MONGODB_URI is not defined in the environment variables.');
  }

  try {
    await mongoose.connect(MONGODB_URI);
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    // Re-throw the error to be handled by the caller
    throw error;
  }
};

export default connectDB;