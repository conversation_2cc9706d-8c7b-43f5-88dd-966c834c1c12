import { useState } from 'react'
import { Plus, Search, Filter, Calendar, Clock, CheckCircle, AlertCircle } from 'lucide-react'

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  
  const projects = [
    {
      id: 1,
      title: 'Wedding Video - Sarah & John',
      client: '<PERSON>',
      status: 'In Progress',
      progress: 75,
      dueDate: '2025-06-15',
      startDate: '2025-05-01',
      budget: 5000,
      description: 'Complete wedding videography including ceremony and reception'
    },
    {
      id: 2,
      title: 'Corporate Event - TechCorp Annual Meeting',
      client: 'TechCorp Solutions',
      status: 'Review',
      progress: 90,
      dueDate: '2025-06-20',
      startDate: '2025-05-15',
      budget: 8500,
      description: 'Multi-camera setup for corporate annual meeting'
    },
    {
      id: 3,
      title: 'Music Video - "Summer Dreams"',
      client: 'Local Music Band',
      status: 'Completed',
      progress: 100,
      dueDate: '2025-05-30',
      startDate: '2025-04-10',
      budget: 3000,
      description: 'Creative music video with outdoor locations'
    },
    {
      id: 4,
      title: 'Product Launch Video',
      client: 'TechCorp Solutions',
      status: 'Planning',
      progress: 25,
      dueDate: '2025-07-01',
      startDate: '2025-06-01',
      budget: 6000,
      description: 'High-end product showcase video'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'In Progress':
        return <Clock className="h-5 w-5 text-blue-500" />
      case 'Review':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
      default:
        return <Calendar className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800'
      case 'In Progress':
        return 'bg-blue-100 text-blue-800'
      case 'Review':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.client.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || project.status.toLowerCase() === statusFilter.toLowerCase()
    return matchesSearch && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Projects</h1>
              <p className="text-[var(--color-steel-blue)] mt-2">Manage your video production projects</p>
            </div>
            <button className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>New Project</span>
            </button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[var(--color-muted-blue-grey)]" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="planning">Planning</option>
              <option value="in progress">In Progress</option>
              <option value="review">Review</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredProjects.map((project) => (
            <div key={project.id} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 hover:shadow-xl transition-shadow">
              {/* Project Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)] mb-1">{project.title}</h3>
                  <p className="text-sm text-[var(--color-steel-blue)]">Client: {project.client}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(project.status)}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>
              </div>

              {/* Project Description */}
              <p className="text-sm text-[var(--color-steel-blue)] mb-4">{project.description}</p>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-[var(--color-dark-indigo)]">Progress</span>
                  <span className="text-sm font-medium text-[var(--color-deep-blue)]">{project.progress}%</span>
                </div>
                <div className="w-full bg-[var(--color-light-grey-blue)] rounded-full h-2">
                  <div 
                    className="bg-[var(--color-deep-blue)] h-2 rounded-full transition-all duration-300"
                    style={{ width: `${project.progress}%` }}
                  ></div>
                </div>
              </div>

              {/* Project Details */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-[var(--color-muted-blue-grey)] uppercase tracking-wide">Start Date</p>
                  <p className="text-sm text-[var(--color-dark-indigo)] font-medium">{new Date(project.startDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-xs text-[var(--color-muted-blue-grey)] uppercase tracking-wide">Due Date</p>
                  <p className="text-sm text-[var(--color-dark-indigo)] font-medium">{new Date(project.dueDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-xs text-[var(--color-muted-blue-grey)] uppercase tracking-wide">Budget</p>
                  <p className="text-sm text-[var(--color-dark-indigo)] font-medium">${project.budget.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-xs text-[var(--color-muted-blue-grey)] uppercase tracking-wide">Days Left</p>
                  <p className="text-sm text-[var(--color-dark-indigo)] font-medium">
                    {Math.ceil((new Date(project.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <button className="flex-1 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  View Details
                </button>
                <button className="flex-1 border border-[var(--color-light-grey-blue)] hover:bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-[var(--color-muted-blue-grey)] mb-4" />
            <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">No projects found</h3>
            <p className="text-[var(--color-steel-blue)]">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first project'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default Projects
