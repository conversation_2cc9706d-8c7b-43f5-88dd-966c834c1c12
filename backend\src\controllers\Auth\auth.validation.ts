import { z } from 'zod';

export const registerUserSchema = z.object({
  body: z.object({
    username: z.string({
      required_error: 'Username is required',
    }).min(3, 'Username must be at least 3 characters long'),
    email: z.string({
      required_error: 'Email is required',
    }).email('Invalid email address'),
    password: z.string({
      required_error: 'Password is required',
    }).min(6, 'Password must be at least 6 characters long'),
    role: z.enum(['admin', 'user']).optional(), // Optional, defaults to 'user' in the User model
  }),
});

export type RegisterUserInput = z.infer<typeof registerUserSchema>['body'];

export const loginUserSchema = z.object({
  body: z.object({
    email: z.string({
      required_error: 'Email is required',
    }).email('Invalid email address'),
    password: z.string({
      required_error: 'Password is required',
    }),
  }),
});

export type LoginUserInput = z.infer<typeof loginUserSchema>['body'];
