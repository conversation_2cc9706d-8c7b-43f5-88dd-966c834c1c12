import mongoose, { Schema, Document, Types } from 'mongoose';
import { IInstallationItem } from './InstallationItem'; // Assuming IInstallationItem is exported from InstallationItem.ts

// Define an interface representing a document in MongoDB.
export interface IInstallationTemplate extends Document {
  templateName: string;
  items: Types.ObjectId[] | IInstallationItem[]; // Array of InstallationItem references
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const InstallationTemplateSchema: Schema<IInstallationTemplate> = new Schema(
  {
    templateName: {
      type: String,
      required: [true, 'Template name is required.'],
      trim: true,
      minlength: [3, 'Template name must be at least 3 characters long.'],
      maxlength: [100, 'Template name cannot exceed 100 characters.'],
    },
    items: [
      {
        type: Schema.Types.ObjectId,
        ref: 'InstallationItem', // Reference to the InstallationItem model
        required: true,
      },
    ],
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required.'],
      default: 0,
      min: [0, 'Total amount cannot be negative.'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    // TODO: Consider adding a pre-save hook to calculate totalAmount based on the items array if needed
  }
);

// Create and export the InstallationTemplate model
const InstallationTemplate = mongoose.model<IInstallationTemplate>(
  'InstallationTemplate',
  InstallationTemplateSchema
);

export default InstallationTemplate;
