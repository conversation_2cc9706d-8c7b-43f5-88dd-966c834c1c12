import { Response } from 'express';

/**
 * Send a successful response
 * @param res - Express Response object
 * @param statusCode - HTTP status code (e.g., 200, 201)
 * @param data - Data to send in the response
 * @param message - Optional success message
 * @returns Response object
 */
export const sendSuccess = (
  res: Response,
  statusCode: number,
  data: any,
  message?: string
) => {
  return res.status(statusCode).json({
    success: true,
    message: message || 'Operation successful',
    data,
  });
};

/**
 * Send an error response
 * @param res - Express Response object
 * @param statusCode - HTTP status code (e.g., 400, 404, 500)
 * @param message - Error message
 * @param error - Optional error details
 * @returns Response object
 */
export const sendError = (
  res: Response,
  statusCode: number,
  message: string,
  error?: any
) => {
  const response: {
    success: boolean;
    message: string;
    error?: any;
  } = {
    success: false,
    message,
  };

  if (error) {
    response.error = error;
  }

  return res.status(statusCode).json(response);
};
