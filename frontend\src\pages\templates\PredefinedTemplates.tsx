import { useState, useEffect, useMemo } from 'react';
import { Plus, Search, Filter, AlertCircle, Loader2, Package, Wrench, HardHat, Truck } from 'lucide-react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import { templatesApi, ApiError } from '../../services/api';
import type { PredefinedTemplate, Equipment, Accessory, Installation, Service } from '../../services/api';

const TEMPLATE_TYPES = ['Equipment', 'Accessory', 'Installation', 'Service'] as const;
type TemplateType = typeof TEMPLATE_TYPES[number];

const PredefinedTemplates = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [templates, setTemplates] = useState<PredefinedTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplateTypes, setSelectedTemplateTypes] = useState<TemplateType[]>([]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      const [equipmentsRes, accessoriesRes, installationsRes, servicesRes] = await Promise.all([
        templatesApi.getEquipments(),
        templatesApi.getAccessories(),
        templatesApi.getInstallations(),
        templatesApi.getServices(),
      ]);

      const allTemplates: PredefinedTemplate[] = [];

      if (equipmentsRes && equipmentsRes.data && Array.isArray(equipmentsRes.data.equipments)) {
        allTemplates.push(...equipmentsRes.data.equipments.map(e => ({ ...e, templateType: 'Equipment' as const })));
      }
      if (accessoriesRes && accessoriesRes.data && Array.isArray(accessoriesRes.data.accessories)) {
        allTemplates.push(...accessoriesRes.data.accessories.map(a => ({ ...a, templateType: 'Accessory' as const })));
      }
      if (installationsRes && installationsRes.data && Array.isArray(installationsRes.data.installations)) {
        allTemplates.push(...installationsRes.data.installations.map(i => ({ ...i, templateType: 'Installation' as const })));
      }
      if (servicesRes && servicesRes.data && Array.isArray(servicesRes.data.services)) {
        allTemplates.push(...servicesRes.data.services.map(s => ({ ...s, templateType: 'Service' as const })));
      }
      
      setTemplates(allTemplates);
    } catch (err) {
      console.error('Error fetching templates:', err);
      if (err instanceof ApiError) {
        setError(err.message);
        toast.error(`Failed to load templates: ${err.message}`);
      } else {
        setError('Failed to load templates');
        toast.error('Failed to load templates');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  const handleTemplateTypeToggle = (type: TemplateType) => {
    setSelectedTemplateTypes(prev =>
      prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
    );
  };

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearchTerm = template.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedTemplateTypes.length === 0 || selectedTemplateTypes.includes(template.templateType);
      return matchesSearchTerm && matchesType;
    });
  }, [templates, searchTerm, selectedTemplateTypes]);

  const getTemplateIcon = (type: TemplateType) => {
    switch (type) {
      case 'Equipment': return <Package className="h-5 w-5" />;
      case 'Accessory': return <Wrench className="h-5 w-5" />;
      case 'Installation': return <HardHat className="h-5 w-5" />;
      case 'Service': return <Truck className="h-5 w-5" />;
      default: return <Package className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-[var(--color-deep-blue)] animate-spin mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Loading templates...</h3>
          <p className="text-[var(--color-steel-blue)]">Please wait while we fetch your template data</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Error loading templates</h3>
          <p className="text-[var(--color-steel-blue)] mb-4">{error}</p>
          <button
            onClick={fetchTemplates}
            className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Predefined Templates</h1>
              <p className="text-[var(--color-steel-blue)] mt-2">Manage your predefined templates</p>
            </div>
            <div className="flex items-center space-x-2">
              <Link
                to="/templates/add" // Assuming a route for adding new templates
                className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                <Plus className="h-5 w-5" />
                <span>Add Template</span>
              </Link>
              <Link
                to="/templates/items/add" // Assuming a route for adding new items
                className="bg-green-600 hover:bg-green-700 text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2 ml-4"
              >
                <Plus className="h-5 w-5" />
                <span>Add Items</span>
              </Link>
            </div>
          </div>
        </div>

        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[var(--color-muted-blue-grey)]" />
              <input
                type="text"
                placeholder="Search templates by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-[var(--color-steel-blue)]" />
              <span className="text-[var(--color-steel-blue)] font-medium">Filter by Type:</span>
              {TEMPLATE_TYPES.map(type => (
                <button
                  key={type}
                  onClick={() => handleTemplateTypeToggle(type)}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                    selectedTemplateTypes.includes(type)
                      ? 'bg-[var(--color-deep-blue)] text-[var(--color-white)]'
                      : 'bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] hover:bg-[var(--color-muted-blue-grey)]'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </div>

        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-[var(--color-muted-blue-grey)] mb-4" />
            <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">No templates found</h3>
            <p className="text-[var(--color-steel-blue)]">
              {searchTerm || selectedTemplateTypes.length > 0 ? 'Try adjusting your search or filter terms' : 'Get started by adding your first template'}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <div key={template._id} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 hover:shadow-xl transition-shadow">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-10 h-10 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center text-[var(--color-white)]">
                  {getTemplateIcon(template.templateType)}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-[var(--color-dark-indigo)]">{template.name}</h3>
                  <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${
                    template.templateType === 'Equipment' ? 'bg-blue-100 text-blue-800' :
                    template.templateType === 'Accessory' ? 'bg-purple-100 text-purple-800' :
                    template.templateType === 'Installation' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-pink-100 text-pink-800' // Service
                  }`}>
                    {template.templateType}
                  </span>
                </div>
              </div>

              <p className="text-sm text-[var(--color-steel-blue)] mb-2 h-10 overflow-hidden">
                {template.description || 'No description available.'}
              </p>
              
              {/* Display type-specific details */}
              <div className="text-xs text-[var(--color-muted-blue-grey)] space-y-1 mb-4">
                {template.templateType === 'Equipment' && (template as Equipment).category && (
                  <p>Category: {(template as Equipment).category}</p>
                )}
                 {template.templateType === 'Equipment' && (template as Equipment).rentalRate !== undefined && (
                  <p>Rental Rate: ${(template as Equipment).rentalRate?.toFixed(2)}</p>
                )}
                {template.templateType === 'Accessory' && (template as Accessory).cost !== undefined && (
                  <p>Cost: ${(template as Accessory).cost?.toFixed(2)}</p>
                )}
                {template.templateType === 'Installation' && (template as Installation).estimatedTime && (
                  <p>Est. Time: {(template as Installation).estimatedTime}</p>
                )}
                {template.templateType === 'Service' && (template as Service).hourlyRate !== undefined && (
                  <p>Hourly Rate: ${(template as Service).hourlyRate?.toFixed(2)}</p>
                )}
              </div>

              <div className="flex space-x-2">
                <button className="flex-1 bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  View Details
                </button>
                <button className="flex-1 border border-[var(--color-light-grey-blue)] hover:bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] py-2 px-3 rounded-lg text-sm font-medium transition-colors">
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PredefinedTemplates;

