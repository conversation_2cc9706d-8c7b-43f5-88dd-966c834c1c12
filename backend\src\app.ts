import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { errorHandler, requestLogger, corsOptions, sendResponse } from './middleware';
import { generateId } from './utils';
import { ApiResponse } from './types';
import apiRoutes from './routes';

// Load environment variables
dotenv.config();

class App {
  public app: Application;
  public port: number;
  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '8000');
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }
  private initializeMiddlewares(): void {
    this.app.use(cors(corsOptions));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(requestLogger);
  }
  
  private initializeRoutes(): void {
    // Health check route
    this.app.get('/health', (req: Request, res: Response) => {
      sendResponse(res, 200, {
        status: 'OK',
        timestamp: new Date().toISOString(),
        requestId: generateId(),
      }, 'Cinepanda Backend is running!');
    });
    
    // API routes
    this.app.get('/api', (req: Request, res: Response) => {
      sendResponse(res, 200, {
        message: 'Welcome to Cinepanda API',
        version: '1.0.0',
        endpoints: [
          'GET /health - Health check',
          'GET /api - API information',
          'POST /api/auth/register - Register user',
          'POST /api/auth/login - Login user',
          'GET /api/auth/me - Get current user (protected)',
          'GET /api/projects - Get all projects (protected)',
          'GET /api/clients - Get all clients (protected)',
          'GET /api/templates - Templates management (protected)',
        ],
      });
    });

    // Mount API routes
    this.app.use('/api', apiRoutes);

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      sendResponse(res, 404, null, `Route ${req.originalUrl} not found`);
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public listen(): void {
    this.app.listen(this.port, () => {
      console.log(`🚀 Cinepanda Backend server is running on http://localhost:${this.port}`);
    });
  }
}

export default App;