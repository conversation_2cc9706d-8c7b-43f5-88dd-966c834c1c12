import React, { useState, useEffect, useMemo } from 'react';
import { Plus, Search, Filter, AlertCircle, Loader2, Package, Wrench, HardHat, Truck, Edit3, Trash2, Eye } from 'lucide-react';
import toast from 'react-hot-toast';
import { templateItemsApi, ApiError } from '../../../services/api'; // Adjusted import path
import type { PredefinedTemplateItem, EquipmentItem, AccessoryItem, InstallationItem, ServiceItem } from '../../../services/api'; // Adjusted import path

const ITEM_TYPES = ['Equipment', 'Accessory', 'Installation', 'Service'] as const;
type ItemType = typeof ITEM_TYPES[number];

type EditableItemData = EquipmentItem | AccessoryItem | InstallationItem | ServiceItem;

// Define all possible keys from all item types for form validation
type AllItemKeys = keyof EquipmentItem | keyof AccessoryItem | keyof InstallationItem | keyof ServiceItem;

// Modal component (basic structure)
interface ItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemType: ItemType | null;
  itemData: PredefinedTemplateItem | null; // For editing existing items
  onSubmit: (formData: Partial<EditableItemData>) => void;
}

type FormErrors = Partial<Record<AllItemKeys, string>>;

const ItemModal: React.FC<ItemModalProps> = ({ isOpen, onClose, itemType, itemData, onSubmit }) => {
  const [formData, setFormData] = useState<Partial<EditableItemData>>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});

  useEffect(() => {
    if (itemData) {
      // Pre-fill form if editing
      const { itemType: _, ...dataToEdit } = itemData; // eslint-disable-line @typescript-eslint/no-unused-vars
      setFormData(dataToEdit);
    } else {
      // Reset for new item
      if (itemType === 'Equipment') {
        setFormData({
          itemName: '',
          specification: '',
          quantity: 1, // Default to 1
          quantityType: 'individual', // Default value
          price: 0,
        });
      } else if (itemType === 'Accessory') {
        setFormData({
          itemName: '',
          quantity: 1, // Default to 1
          quantityType: 'individual', // Default value
          amount: 0,
        });
      } else if (itemType === 'Installation') {
        setFormData({
          name: '',
          price: 0,
        });
      } else if (itemType === 'Service') {
        setFormData({
          serviceName: '',
          areaInSquareFeet: 0,
          pricePerSquareFoot: 0,
          totalPrice: 0, // Initial value, will be auto-calculated
        });
      } else {
        setFormData({}); // Fallback for unknown or null itemType
      }
    }
  }, [itemData, isOpen, itemType]);

  // Effect to auto-calculate totalPrice for Service items
  useEffect(() => {
    if (itemType === 'Service') {
      const serviceFormData = formData as Partial<ServiceItem>; // Cast for type safety
      const area = serviceFormData.areaInSquareFeet;
      const pricePerSqFt = serviceFormData.pricePerSquareFoot;

      let newTotalPrice = 0; // Default to 0 if inputs are not valid
      if (typeof area === 'number' && typeof pricePerSqFt === 'number' && !isNaN(area) && !isNaN(pricePerSqFt)) {
        newTotalPrice = area * pricePerSqFt;
      }

      // Only update if the calculated totalPrice is different to prevent potential infinite loops
      if (serviceFormData.totalPrice !== newTotalPrice) {
        setFormData(prev => ({
          ...prev,
          totalPrice: newTotalPrice,
        }));
      }
    }
  }, [formData, itemType]); // Rerun when formData or itemType changes
  const validateForm = (): FormErrors => {
    const errors: FormErrors = {};
    if (!itemType) return errors;

    switch (itemType) {
      case 'Equipment': {
        const equipmentData = formData as Partial<EquipmentItem>;
        if (!equipmentData.itemName?.trim()) {
          errors.itemName = 'Item name is required.';
        }
        if (equipmentData.quantity === undefined || equipmentData.quantity === null || equipmentData.quantity.toString().trim() === '') {
          errors.quantity = 'Quantity is required.';
        } else if (equipmentData.quantity < 0) {
          errors.quantity = 'Quantity cannot be negative.';
        }
        if (!equipmentData.quantityType) {
          errors.quantityType = 'Quantity type is required.';
        }
        if (equipmentData.price === undefined || equipmentData.price === null || equipmentData.price.toString().trim() === '') {
          errors.price = 'Price is required.';
        } else if (equipmentData.price < 0) {
          errors.price = 'Price cannot be negative.';
        }
        break;
      }
      case 'Accessory': {
        const accessoryData = formData as Partial<AccessoryItem>;
        if (!accessoryData.itemName?.trim()) {
          errors.itemName = 'Item name is required.';
        }
        if (accessoryData.quantity === undefined || accessoryData.quantity === null || accessoryData.quantity.toString().trim() === '') {
          errors.quantity = 'Quantity is required.';
        } else if (accessoryData.quantity < 1) {
          errors.quantity = 'Quantity must be at least 1.';
        }
        if (!accessoryData.quantityType) {
          errors.quantityType = 'Quantity type is required.';
        }
        if (accessoryData.amount === undefined || accessoryData.amount === null || accessoryData.amount.toString().trim() === '') {
          errors.amount = 'Amount is required.';
        } else if (accessoryData.amount < 0) {
          errors.amount = 'Amount cannot be negative.';
        }
        break;
      }
      case 'Installation': {
        const installationData = formData as Partial<InstallationItem>;
        if (!installationData.name?.trim()) {
          errors.name = 'Name is required.';
        } else if (installationData.name.trim().length < 3) {
          errors.name = 'Name must be at least 3 characters.';
        }
        if (installationData.price === undefined || installationData.price === null || installationData.price.toString().trim() === '') {
          errors.price = 'Price is required.';
        } else if (installationData.price < 0) {
          errors.price = 'Price cannot be negative.';
        }
        break;
      }
      case 'Service': {
        const serviceData = formData as Partial<ServiceItem>;
        if (!serviceData.serviceName?.trim()) {
          errors.serviceName = 'Service name is required.';
        }
        if (serviceData.areaInSquareFeet === undefined || serviceData.areaInSquareFeet === null || serviceData.areaInSquareFeet.toString().trim() === '') {
          errors.areaInSquareFeet = 'Area is required.';
        } else if (serviceData.areaInSquareFeet < 0) {
          errors.areaInSquareFeet = 'Area cannot be negative.';
        }
        if (serviceData.pricePerSquareFoot === undefined || serviceData.pricePerSquareFoot === null || serviceData.pricePerSquareFoot.toString().trim() === '') {
          errors.pricePerSquareFoot = 'Price per sq ft is required.';
        } else if (serviceData.pricePerSquareFoot < 0) {
          errors.pricePerSquareFoot = 'Price per sq ft cannot be negative.';
        }
        break;
      }
    }
    return errors;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type: elementType } = e.target;
    const isCheckbox = elementType === 'checkbox';
    let val: string | number | boolean | undefined = value;

    if (isCheckbox) {
      val = (e.target as HTMLInputElement).checked;
    } else if (elementType === 'number' || e.target.type === 'number') {
      // For number inputs, parse to float. If parsing fails (e.g., empty string or invalid chars), 
      // it results in NaN. The effect calculating totalPrice will handle NaN inputs by setting totalPrice to 0.
      val = parseFloat(value);
    }
    
    setFormData((prev: Partial<EditableItemData>) => ({ ...prev, [name]: val }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      toast.error('Please correct the form errors.');
      return;
    }
    setFormErrors({}); // Clear errors if validation passes
    onSubmit(formData);
    onClose(); // Close modal after submit
  };

  if (!isOpen || !itemType) return null;

  const renderFormFields = () => {
    switch (itemType) {
      case 'Equipment':
        return (
          <>
            <div className="mb-4">
              <label htmlFor="itemName" className="block text-sm font-medium text-gray-700 mb-1">Item Name</label>
              <input type="text" name="itemName" id="itemName" value={(formData as EquipmentItem).itemName || ''} onChange={handleChange} required className={`w-full px-3 py-2 border ${formErrors.itemName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.itemName && <p className="text-red-500 text-xs mt-1">{formErrors.itemName}</p>}
            </div>
            <div className="mb-4">
              <label htmlFor="specification" className="block text-sm font-medium text-gray-700 mb-1">Specification (Optional)</label>
              <input type="text" name="specification" id="specification" value={(formData as EquipmentItem).specification || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" />
            </div>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                <input type="number" name="quantity" id="quantity" value={(formData as EquipmentItem).quantity ?? ''} onChange={handleChange} required min="0" className={`w-full px-3 py-2 border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
                {formErrors.quantity && <p className="text-red-500 text-xs mt-1">{formErrors.quantity}</p>}
              </div>
              <div>
                <label htmlFor="quantityType" className="block text-sm font-medium text-gray-700 mb-1">Quantity Type</label>
                <select name="quantityType" id="quantityType" value={(formData as EquipmentItem).quantityType || 'individual'} onChange={handleChange} required className={`w-full px-3 py-2 border ${formErrors.quantityType ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}>
                  <option value="individual">Individual</option>
                  <option value="set">Set</option>
                </select>
                {formErrors.quantityType && <p className="text-red-500 text-xs mt-1">{formErrors.quantityType}</p>}              </div>
            </div>
            <div className="mb-4">
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">Price</label>
              <input type="number" name="price" id="price" value={(formData as EquipmentItem).price ?? ''} onChange={handleChange} required min="0" step="0.01" className={`w-full px-3 py-2 border ${formErrors.price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.price && <p className="text-red-500 text-xs mt-1">{formErrors.price}</p>}
            </div>
          </>
        );
      case 'Accessory':
        return (
          <>
            <div className="mb-4">
              <label htmlFor="itemName" className="block text-sm font-medium text-gray-700 mb-1">Item Name</label>
              <input type="text" name="itemName" id="itemName" value={(formData as AccessoryItem).itemName || ''} onChange={handleChange} required className={`w-full px-3 py-2 border ${formErrors.itemName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.itemName && <p className="text-red-500 text-xs mt-1">{formErrors.itemName}</p>}
            </div>
             <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                <input type="number" name="quantity" id="quantity" value={(formData as AccessoryItem).quantity ?? ''} onChange={handleChange} required min="1" className={`w-full px-3 py-2 border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
                {formErrors.quantity && <p className="text-red-500 text-xs mt-1">{formErrors.quantity}</p>}
              </div>
              <div>
                <label htmlFor="quantityType" className="block text-sm font-medium text-gray-700 mb-1">Quantity Type</label>
                <select name="quantityType" id="quantityType" value={(formData as AccessoryItem).quantityType || 'individual'} onChange={handleChange} required className={`w-full px-3 py-2 border ${formErrors.quantityType ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}>
                  <option value="individual">Individual</option>
                  <option value="set">Set</option>
                </select>
                {formErrors.quantityType && <p className="text-red-500 text-xs mt-1">{formErrors.quantityType}</p>}
              </div>
            </div>            <div className="mb-4">
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
              <input type="number" name="amount" id="amount" value={(formData as AccessoryItem).amount ?? ''} onChange={handleChange} required min="0" step="0.01" className={`w-full px-3 py-2 border ${formErrors.amount ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.amount && <p className="text-red-500 text-xs mt-1">{formErrors.amount}</p>}
            </div>
          </>
        );
      case 'Installation':
        return (
          <>            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Installation Item Name</label>
              <input type="text" name="name" id="name" value={(formData as InstallationItem).name || ''} onChange={handleChange} required minLength={3} maxLength={100} className={`w-full px-3 py-2 border ${formErrors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.name && <p className="text-red-500 text-xs mt-1">{formErrors.name}</p>}
            </div>            <div className="mb-4">
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">Price</label>
              <input type="number" name="price" id="price" value={(formData as InstallationItem).price ?? ''} onChange={handleChange} required min="0" step="0.01" className={`w-full px-3 py-2 border ${formErrors.price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.price && <p className="text-red-500 text-xs mt-1">{formErrors.price}</p>}
            </div>
          </>
        );
      case 'Service':
        return (
          <>
            <div className="mb-4">
              <label htmlFor="serviceName" className="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
              <input type="text" name="serviceName" id="serviceName" value={(formData as ServiceItem).serviceName || ''} onChange={handleChange} required className={`w-full px-3 py-2 border ${formErrors.serviceName ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.serviceName && <p className="text-red-500 text-xs mt-1">{formErrors.serviceName}</p>}
            </div>
            <div className="mb-4">
              <label htmlFor="areaInSquareFeet" className="block text-sm font-medium text-gray-700 mb-1">Area (sq ft)</label>
              <input type="number" name="areaInSquareFeet" id="areaInSquareFeet" value={(formData as ServiceItem).areaInSquareFeet ?? ''} onChange={handleChange} required min="0" className={`w-full px-3 py-2 border ${formErrors.areaInSquareFeet ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.areaInSquareFeet && <p className="text-red-500 text-xs mt-1">{formErrors.areaInSquareFeet}</p>}
            </div>
            <div className="mb-4">
              <label htmlFor="pricePerSquareFoot" className="block text-sm font-medium text-gray-700 mb-1">Price per sq ft</label>
              <input type="number" name="pricePerSquareFoot" id="pricePerSquareFoot" value={(formData as ServiceItem).pricePerSquareFoot ?? ''} onChange={handleChange} required min="0" step="0.01" className={`w-full px-3 py-2 border ${formErrors.pricePerSquareFoot ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`} />
              {formErrors.pricePerSquareFoot && <p className="text-red-500 text-xs mt-1">{formErrors.pricePerSquareFoot}</p>}
            </div>
            <div className="mb-4">
              <label htmlFor="totalPrice" className="block text-sm font-medium text-gray-700 mb-1">Total Price</label>
              <input type="number" name="totalPrice" id="totalPrice" value={(formData as ServiceItem).totalPrice?.toFixed(2) || '0.00'} onChange={handleChange} required min="0" step="0.01" className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-gray-100" readOnly />
            </div>
          </>
        );
      default: return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50">
      <div className="relative bg-white p-8 rounded-xl shadow-xl w-full max-w-lg mx-auto">
        <button onClick={onClose} className="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
          <Plus className="h-6 w-6 rotate-45" /> {/* Using Plus icon rotated for close */}
        </button>
        <h2 className="text-2xl font-semibold text-[var(--color-dark-indigo)] mb-6">
          {itemData ? 'Edit' : 'Add'} {itemType} Item
        </h2>
        <form onSubmit={handleSubmit}>
          {renderFormFields()}
          <div className="mt-6 flex justify-end space-x-3">
            <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md border border-gray-300">
              Cancel
            </button>
            <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] rounded-md">
              {itemData ? 'Save Changes' : 'Add Item'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const PredefinedTemplateItems: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [items, setItems] = useState<PredefinedTemplateItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItemTypes, setSelectedItemTypes] = useState<ItemType[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentItemTypeForModal, setCurrentItemTypeForModal] = useState<ItemType | null>(null);
  const [editingItem, setEditingItem] = useState<PredefinedTemplateItem | null>(null);

  const fetchItems = async () => {
    try {
      setLoading(true);
      setError(null);
      const [equipmentsRes, accessoriesRes, installationsRes, servicesRes] = await Promise.all([
        templateItemsApi.getEquipmentItems(),
        templateItemsApi.getAccessoryItems(),
        templateItemsApi.getInstallationItems(),
        templateItemsApi.getServiceItems(),
      ]);

      console.log('servicesRes', servicesRes);
      console.log('equipmentsRes', equipmentsRes);

      const allItems: PredefinedTemplateItem[] = [];

      if (equipmentsRes?.data?.items) {
        allItems.push(...equipmentsRes.data.items.map(e => ({ ...e, itemType: 'Equipment' as const })));
      }
      if (accessoriesRes?.data?.items) {
        allItems.push(...accessoriesRes.data.items.map(a => ({ ...a, itemType: 'Accessory' as const })));
      }
      if (installationsRes?.data?.items) {
        allItems.push(...installationsRes.data.items.map(i => ({ ...i, itemType: 'Installation' as const })));
      }
      if (servicesRes?.data?.items) {
        allItems.push(...servicesRes.data.items.map(s => ({ ...s, itemType: 'Service' as const })));
      }
      
      setItems(allItems);
    } catch (err) {
      console.error('Error fetching template items:', err);
      const apiError = err as ApiError; // Type assertion
      setError(apiError.message || 'Failed to load template items');
      toast.error(`Failed to load items: ${apiError.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  const handleItemTypeToggle = (type: ItemType) => {
    setSelectedItemTypes(prev =>
      prev.includes(type) ? prev.filter(t => t !== type) : [...prev, type]
    );
  };

  const openModalToAdd = (type: ItemType) => {
    setCurrentItemTypeForModal(type);
    setEditingItem(null);
    setIsModalOpen(true);
  };

  const openModalToEdit = (item: PredefinedTemplateItem) => {
    setCurrentItemTypeForModal(item.itemType);
    setEditingItem(item);
    setIsModalOpen(true);
  };

  const handleModalSubmit = async (formData: Partial<EditableItemData>) => {
    if (!currentItemTypeForModal) {
      toast.error('Item type is not selected.');
      return;
    }

    setLoading(true); // Consider a more granular loading state for the modal
    try {
      if (editingItem && editingItem._id) {
        const itemId = editingItem._id;
        // Update existing item
        switch (currentItemTypeForModal) {
          case 'Equipment':
            await templateItemsApi.updateEquipmentItem(itemId, formData as Partial<Omit<EquipmentItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>);
            break;
          case 'Accessory':
            await templateItemsApi.updateAccessoryItem(itemId, formData as Partial<Omit<AccessoryItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>);
            break;
          case 'Installation':
            await templateItemsApi.updateInstallationItem(itemId, formData as Partial<Omit<InstallationItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>);
            break;
          case 'Service':
            await templateItemsApi.updateServiceItem(itemId, formData as Partial<Omit<ServiceItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>>);
            break;
          default:
            toast.error('Invalid item type for update.');
            setLoading(false);
            return;
        }
        toast.success(`${currentItemTypeForModal} item updated successfully!`);
      } else {
        // Create new item
        switch (currentItemTypeForModal) {
          case 'Equipment':
            await templateItemsApi.createEquipmentItem(formData as Omit<EquipmentItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>);
            break;
          case 'Accessory':
            await templateItemsApi.createAccessoryItem(formData as Omit<AccessoryItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>);
            break;
          case 'Installation':
            await templateItemsApi.createInstallationItem(formData as Omit<InstallationItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>);
            break;
          case 'Service':
            await templateItemsApi.createServiceItem(formData as Omit<ServiceItem, '_id' | 'createdAt' | 'updatedAt' | 'isPredefined'>);
            break;
          default:
            toast.error('Invalid item type for creation.');
            setLoading(false);
            return;
        }
        toast.success(`${currentItemTypeForModal} item added successfully!`);
      }
      fetchItems(); // Re-fetch items to update the list
      setIsModalOpen(false); // Close modal on success
    } catch (err) {
      console.error('Error submitting item:', err);
      const apiError = err as ApiError;
      toast.error(`Failed to save item: ${apiError.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteItem = async (itemId: string, itemType: ItemType) => {
    if (!confirm(`Are you sure you want to delete this ${itemType} item?`)) {
      return;
    }
    setLoading(true); // Or a specific deleting state
    try {
      switch (itemType) {
        case 'Equipment':
          await templateItemsApi.deleteEquipmentItem(itemId);
          break;
        case 'Accessory':
          await templateItemsApi.deleteAccessoryItem(itemId);
          break;
        case 'Installation':
          await templateItemsApi.deleteInstallationItem(itemId);
          break;
        case 'Service':
          await templateItemsApi.deleteServiceItem(itemId);
          break;
        default:
          toast.error('Invalid item type for deletion.');
          setLoading(false);
          return;
      }
      toast.success(`${itemType} item deleted successfully!`);
      fetchItems(); // Re-fetch items to update the list
    } catch (err) {
      console.error('Error deleting item:', err);
      const apiError = err as ApiError;
      toast.error(`Failed to delete item: ${apiError.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = useMemo(() => {
    return items.filter(item => {
      const name = item.itemType === 'Installation' ? (item as InstallationItem).name :
                   item.itemType === 'Service' ? (item as ServiceItem).serviceName :
                   (item as EquipmentItem | AccessoryItem).itemName;
      const matchesSearchTerm = name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedItemTypes.length === 0 || selectedItemTypes.includes(item.itemType);
      return matchesSearchTerm && matchesType;
    });
  }, [items, searchTerm, selectedItemTypes]);

  const getItemIcon = (type: ItemType) => {
    switch (type) {
      case 'Equipment': return <Package className="h-5 w-5" />;
      case 'Accessory': return <Wrench className="h-5 w-5" />;
      case 'Installation': return <HardHat className="h-5 w-5" />;
      case 'Service': return <Truck className="h-5 w-5" />;
      default: return <Package className="h-5 w-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center p-8">
        <div className="text-center">
          <Loader2 className="mx-auto h-12 w-12 text-[var(--color-deep-blue)] animate-spin mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Loading Items...</h3>
          <p className="text-[var(--color-steel-blue)]">Fetching predefined template items.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">Error Loading Items</h3>
          <p className="text-[var(--color-steel-blue)] mb-4">{error}</p>
          <button
            onClick={fetchItems}
            className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Predefined Template Items</h1>
              <p className="text-[var(--color-steel-blue)] mt-2">Manage items for your predefined templates.</p>
            </div>
            {/* Button to add new item - opens modal for selecting type first, or a default type */}
            <div className="flex space-x-2">
                {ITEM_TYPES.map(type => (
                    <button
                        key={type}
                        onClick={() => openModalToAdd(type)}
                        className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                    >
                        <Plus className="h-5 w-5" />
                        <span>Add {type} Item</span>
                    </button>
                ))}
            </div>
          </div>
        </div>

        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[var(--color-muted-blue-grey)]" />
              <input
                type="text"
                placeholder="Search items by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent"
              />
            </div>
            <div className="flex items-center space-x-2 flex-wrap">
              <Filter className="h-5 w-5 text-[var(--color-steel-blue)]" />
              <span className="text-[var(--color-steel-blue)] font-medium">Filter by Type:</span>
              {ITEM_TYPES.map(type => (
                <button
                  key={type}
                  onClick={() => handleItemTypeToggle(type)}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors mt-2 sm:mt-0 ${ // Added margin top for smaller screens
                    selectedItemTypes.includes(type)
                      ? 'bg-[var(--color-deep-blue)] text-[var(--color-white)]'
                      : 'bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] hover:bg-[var(--color-muted-blue-grey)]'
                  }`}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-[var(--color-muted-blue-grey)] mb-4" />
            <h3 className="text-lg font-medium text-[var(--color-dark-indigo)] mb-2">No items found</h3>
            <p className="text-[var(--color-steel-blue)]">
              {searchTerm || selectedItemTypes.length > 0 ? 'Try adjusting your search or filter terms' : 'Get started by adding your first item'}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map((item) => {
            const itemName = item.itemType === 'Installation' ? (item as InstallationItem).name :
                             item.itemType === 'Service' ? (item as ServiceItem).serviceName :
                             (item as EquipmentItem | AccessoryItem).itemName;
            // const itemDescription = item.itemType === 'Equipment' ? (item as EquipmentItem).specification : ''; // Basic description example

            return (
              <div key={item._id} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-5 flex flex-col justify-between hover:shadow-xl transition-shadow">
                <div>
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center text-[var(--color-white)] flex-shrink-0">
                      {getItemIcon(item.itemType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-md text-[var(--color-dark-indigo)] truncate" title={itemName}>{itemName}</h3>
                      <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${ // Consistent badge styling
                        item.itemType === 'Equipment' ? 'bg-blue-100 text-blue-800' :
                        item.itemType === 'Accessory' ? 'bg-purple-100 text-purple-800' :
                        item.itemType === 'Installation' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-pink-100 text-pink-800' // Service
                      }`}>
                        {item.itemType}
                      </span>
                    </div>
                  </div>

                  {/* {itemDescription && (
                    <p className=\"text-xs text-[var(--color-steel-blue)] mb-2 h-8 overflow-hidden\">
                      {itemDescription}
                    </p>
                  )} */}
                  
                  <div className="text-xs text-[var(--color-muted-blue-grey)] space-y-0.5 mb-3">
                    {item.itemType === 'Equipment' && (
                        <>
                            {(item as EquipmentItem).specification && <p className="truncate" title={(item as EquipmentItem).specification}>Spec: {(item as EquipmentItem).specification}</p>}
                            <p>Qty: {(item as EquipmentItem).quantity} {(item as EquipmentItem).quantityType}</p>
                            <p>Price: ${(item as EquipmentItem).price.toFixed(2)}</p>
                        </>
                    )}
                    {item.itemType === 'Accessory' && (
                        <>
                            <p>Qty: {(item as AccessoryItem).quantity} {(item as AccessoryItem).quantityType}</p>
                            <p>Amount: ${(item as AccessoryItem).amount.toFixed(2)}</p>
                        </>
                    )}
                    {item.itemType === 'Installation' && (
                        <p>Price: ${(item as InstallationItem).price.toFixed(2)}</p>
                    )}
                    {item.itemType === 'Service' && (
                        <>
                            <p>Area: {(item as ServiceItem).areaInSquareFeet} sq ft</p>
                            <p>Rate: ${(item as ServiceItem).pricePerSquareFoot.toFixed(2)}/sq ft</p>
                            <p>Total: ${(item as ServiceItem).totalPrice.toFixed(2)}</p>
                        </>
                    )}
                  </div>
                </div>

                <div className="flex space-x-2 mt-auto pt-3 border-t border-gray-200">
                  <button 
                    onClick={() => openModalToEdit(item)}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-1.5 px-2 rounded-md text-xs font-medium transition-colors flex items-center justify-center space-x-1">
                    <Edit3 className="h-3.5 w-3.5" />
                    <span>Edit</span>
                  </button>
                  <button 
                    // onClick={() => {/* Implement view details logic if needed */}}
                    className="flex-1 border border-gray-300 hover:bg-gray-100 text-gray-700 py-1.5 px-2 rounded-md text-xs font-medium transition-colors flex items-center justify-center space-x-1">
                    <Eye className="h-3.5 w-3.5" />
                    <span>View</span>
                  </button>
                  <button 
                    onClick={() => handleDeleteItem(item._id, item.itemType)}
                    className="flex-1 bg-red-500 hover:bg-red-600 text-white py-1.5 px-2 rounded-md text-xs font-medium transition-colors flex items-center justify-center space-x-1">
                    <Trash2 className="h-3.5 w-3.5" />
                    <span>Delete</span>
                  </button>
                </div>
              </div>
            )}
          )}
        </div>
      </div>
      <ItemModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
        itemType={currentItemTypeForModal}
        itemData={editingItem}
        onSubmit={handleModalSubmit}
      />
    </div>
  );
};

export default PredefinedTemplateItems;
