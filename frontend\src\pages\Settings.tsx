import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import { Settings, User, Bell, Shield, Palette, Database } from 'lucide-react'

const SettingsPage = () => {
  const { user } = useAuth()

  const settingsCategories = [
    {
      title: 'Profile Settings',
      icon: User,
      description: 'Manage your personal information and preferences',
      items: [
        { label: 'Personal Information', value: 'Edit profile details' },
        { label: 'Change Password', value: 'Update your password' },
        { label: 'Profile Picture', value: 'Upload new avatar' }
      ]
    },
    {
      title: 'Notification Settings',
      icon: Bell,
      description: 'Configure how you receive notifications',
      items: [
        { label: 'Email Notifications', value: 'Project updates, deadlines' },
        { label: 'Push Notifications', value: 'Browser notifications' },
        { label: 'SMS Alerts', value: 'Important reminders' }
      ]
    },
    {
      title: 'Security & Privacy',
      icon: Shield,
      description: 'Manage your account security settings',
      items: [
        { label: 'Two-Factor Authentication', value: 'Enhanced security' },
        { label: 'Login Sessions', value: 'Active device management' },
        { label: 'Privacy Settings', value: 'Data sharing preferences' }
      ]
    },
    {
      title: 'Application Settings',
      icon: Settings,
      description: 'Customize your application experience',
      items: [
        { label: 'Theme Preferences', value: 'Light/Dark mode' },
        { label: 'Language', value: 'Interface language' },
        { label: 'Time Zone', value: 'Local time settings' }
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)]">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[var(--color-dark-indigo)]">Settings</h1>
          <p className="text-[var(--color-steel-blue)] mt-2">Manage your account and application preferences</p>
        </div>

        {/* User Info Card */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center">
              <span className="text-[var(--color-white)] text-xl font-semibold">
                {user?.username?.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-[var(--color-dark-indigo)]">{user?.username}</h3> 
              <p className="text-[var(--color-steel-blue)]">{user?.email}</p>
              <p className="text-sm text-[var(--color-muted-blue-grey)] mt-1">Member since January 2024</p>
            </div>
            <button className="bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] px-4 py-2 rounded-lg font-medium transition-colors">
              Edit Profile
            </button>
          </div>
        </div>

        {/* Settings Categories */}
        <div className="space-y-6">
          {settingsCategories.map((category, index) => (
            <div key={index} className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6">
              {/* Category Header */}
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-[var(--color-deep-blue)] rounded-lg flex items-center justify-center">
                  <category.icon className="h-6 w-6 text-[var(--color-white)]" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)]">{category.title}</h3>
                  <p className="text-sm text-[var(--color-steel-blue)]">{category.description}</p>
                </div>
              </div>

              {/* Category Items */}
              <div className="space-y-3">
                {category.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center justify-between p-3 bg-[var(--color-light-grey-blue)] rounded-lg hover:bg-opacity-80 transition-colors cursor-pointer">
                    <div>
                      <p className="font-medium text-[var(--color-dark-indigo)]">{item.label}</p>
                      <p className="text-sm text-[var(--color-steel-blue)]">{item.value}</p>
                    </div>
                    <div className="text-[var(--color-muted-blue-grey)]">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Account Actions */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-lg border border-[var(--color-light-grey-blue)] p-6 mt-8">
          <h3 className="text-lg font-semibold text-[var(--color-dark-indigo)] mb-4">Account Actions</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <button className="flex-1 bg-[var(--color-steel-blue)] hover:bg-[var(--color-dark-indigo)] text-[var(--color-white)] py-3 px-4 rounded-lg font-medium transition-colors">
              Export Data
            </button>
            <button className="flex-1 border border-[var(--color-light-grey-blue)] hover:bg-[var(--color-light-grey-blue)] text-[var(--color-steel-blue)] py-3 px-4 rounded-lg font-medium transition-colors">
              Download Backup
            </button>
            <button className="flex-1 bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg font-medium transition-colors">
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
