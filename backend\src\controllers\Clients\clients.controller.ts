import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import Client from '../../models/Client';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { z } from 'zod';
import {
  createClientSchema,
  updateClientSchema,
  searchClientSchema,
  clientIdSchema
} from './clients.validation';

/**
 * @description Get all clients
 * @route GET /api/clients
 * @access Protected (Admin, Manager)
 */
export const getAllClients = async (req: Request, res: Response) => {
  try {
    const clients = await Client.find().sort({ clientName: 1 });

    return sendSuccess(res, 200, {
      count: clients.length,
      clients,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching clients', error);
  }
};

/**
 * @description Get a single client by ID
 * @route GET /api/clients/:id
 * @access Protected (Admin, Manager)
 */
export const getClientById = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate client ID using Zod schema
    const result = clientIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid client ID', { messages });
    }

    const client = await Client.findById(id);
    
    if (!client) {
      return sendError(res, 404, 'Client not found');
    }

    return sendSuccess(res, 200, { client });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching client', error);
  }
};

/**
 * @description Create a new client
 * @route POST /api/clients
 * @access Protected (Admin, Manager)
 */
export const createClient = async (req: Request, res: Response) => {  try {    // Validate request body using Zod schema
    const result = createClientSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { clientName, number, email, place, address, description } = result.data;

    // Check if client with the same email exists (if email is provided)
    if (email) {
      const existingClient = await Client.findOne({ email });
      if (existingClient) {
        return sendError(res, 400, 'A client with this email already exists');
      }
    }

    const newClient = await Client.create({
      clientName,
      number,
      email,
      place,
      address,
      description,
    });

    return sendSuccess(res, 201, { 
      message: 'Client created successfully', 
      client: newClient 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error creating client', error);
  }
};

/**
 * @description Update a client by ID
 * @route PUT /api/clients/:id
 * @access Protected (Admin, Manager)
 */
export const updateClient = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate client ID using Zod schema
    const idValidation = clientIdSchema.safeParse({ id });
    
    if (!idValidation.success) {
      const messages = idValidation.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid client ID', { messages });
    }

    // Validate request body using Zod schema
    const result = updateClientSchema.safeParse(req.body);
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    // Check if the client exists
    const existingClient = await Client.findById(id);
    if (!existingClient) {
      return sendError(res, 404, 'Client not found');
    }

    const updateData = result.data;

    // Check if email is being updated and if it's already in use
    if (updateData.email && updateData.email !== existingClient.email) {
      const emailExists = await Client.findOne({ 
        email: updateData.email,
        _id: { $ne: id } // Exclude the current client from the search
      });
      
      if (emailExists) {
        return sendError(res, 400, 'Email is already in use');
      }
    }

    const updatedClient = await Client.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return sendSuccess(res, 200, { 
      message: 'Client updated successfully', 
      client: updatedClient 
    });
  } catch (error: any) {
    // Handle mongoose validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map((err: any) => err.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    return sendError(res, 500, 'Error updating client', error);
  }
};

/**
 * @description Delete a client by ID
 * @route DELETE /api/clients/:id
 * @access Protected (Admin)
 */
export const deleteClient = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate client ID using Zod schema
    const result = clientIdSchema.safeParse({ id });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid client ID', { messages });
    }

    const client = await Client.findByIdAndDelete(id);
    
    if (!client) {
      return sendError(res, 404, 'Client not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Client deleted successfully',
      clientId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting client', error);
  }
};

/**
 * @description Search clients by name, email or place
 * @route GET /api/clients/search
 * @access Protected (Admin, Manager)
 */
export const searchClients = async (req: Request, res: Response) => {  try {
    // Validate search query using Zod schema
    const result = searchClientSchema.safeParse({ query: req.query.query });
    
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }

    const { query } = result.data;

    const clients = await Client.find({
      $or: [
        { clientName: { $regex: query, $options: 'i' } }, // Case insensitive search
        { email: { $regex: query, $options: 'i' } },
        { place: { $regex: query, $options: 'i' } }
      ]
    }).sort({ clientName: 1 });

    return sendSuccess(res, 200, {
      count: clients.length,
      clients,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error searching clients', error);
  }
};
