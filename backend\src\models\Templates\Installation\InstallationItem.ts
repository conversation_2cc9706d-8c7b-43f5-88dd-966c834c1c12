import mongoose, { Schema, Document } from 'mongoose';

// Define an interface representing a document in MongoDB.
export interface IInstallationItem extends Document {
  name: string;
  price: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const InstallationItemSchema: Schema<IInstallationItem> = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Installation item name is required'],
      trim: true,
      minlength: [3, 'Name must be at least 3 characters long'],
      maxlength: [100, 'Name cannot exceed 100 characters'],
    },
    price: {
      type: Number,
      required: [true, 'Price is required'],
      min: [0, 'Price cannot be negative'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Create and export the Mongoose model.
const InstallationItem = mongoose.model<IInstallationItem>(
  'InstallationItem',
  InstallationItemSchema
);

export default InstallationItem;
