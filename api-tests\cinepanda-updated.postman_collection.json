{"info": {"_postman_id": "7c8e9f2a-6d1b-4f5c-9f3a-b5e221a7d8c2", "name": "Cinepanda API Collection - Updated", "description": "Complete collection for testing all Cinepanda API endpoints with Zod validation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication API endpoints for user registration and login", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"role\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user with the system"}}, {"name": "Register Admin User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\",\n    \"role\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register an admin user with the system"}}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login with a registered user", "event": [{"listen": "test", "script": {"exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('token');", "});", "", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.token) {", "        pm.collectionVariables.set('token', response.data.token);", "        console.log('User token saved:', response.data.token);", "        ", "        // Also save user info for reference", "        if (response.data.user) {", "            pm.collectionVariables.set('userId', response.data.user.id || response.data.user._id);", "            pm.collectionVariables.set('userRole', response.data.user.role);", "            console.log('User info saved - ID:', response.data.user.id || response.data.user._id, 'Role:', response.data.user.role);", "        }", "    } else {", "        console.error('<PERSON><PERSON> failed: No token received');", "    }", "} else {", "    console.error('<PERSON><PERSON> failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"adminPass123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}, "description": "Login with an admin user", "event": [{"listen": "test", "script": {"exec": ["pm.test('Admin login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('token');", "});", "", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.token) {", "        pm.collectionVariables.set('token', response.data.token);", "        console.log('Admin token saved:', response.data.token);", "        ", "        // Also save admin info for reference", "        if (response.data.user) {", "            pm.collectionVariables.set('userId', response.data.user.id || response.data.user._id);", "            pm.collectionVariables.set('userRole', response.data.user.role);", "            console.log('Admin info saved - ID:', response.data.user.id || response.data.user._id, 'Role:', response.data.user.role);", "        }", "    } else {", "        console.error('Admin login failed: No token received');", "    }", "} else {", "    console.error('Admin login failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}, "description": "Get the currently authenticated user's details"}}]}, {"name": "Clients", "description": "Client management endpoints with full CRUD operations", "item": [{"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}, "description": "Get all clients (requires admin/manager permission)"}}, {"name": "Create Client", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"clientName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"number\": \"1234567890\",\n    \"place\": \"New York\",\n    \"address\": \"123 Main Street, New York, NY 10001\",\n    \"description\": \"Corporate client for video production services\"\n}"}, "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}, "description": "Create a new client with <PERSON>od validation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Client creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('client');", "    pm.expect(response.data.client).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.client) {", "        const clientId = response.data.client._id;", "        pm.collectionVariables.set('clientId', clientId);", "        console.log('Client ID saved:', clientId);", "    } else {", "        console.error('Client creation failed: No client data received');", "    }", "} else {", "    console.error('Client creation failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "Get Client by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}, "description": "Get a specific client by ID"}}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"clientName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"number\": \"0987654321\",\n    \"place\": \"Los Angeles\",\n    \"address\": \"456 Updated Street, Los Angeles, CA 90001\",\n    \"description\": \"Updated client information\"\n}"}, "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}, "description": "Update an existing client"}}, {"name": "Delete Client", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/clients/{{clientId}}", "host": ["{{baseUrl}}"], "path": ["api", "clients", "{{clientId}}"]}, "description": "Delete a client (admin only)"}}]}, {"name": "Projects", "description": "Project management endpoints with full CRUD operations", "item": [{"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/projects", "host": ["{{baseUrl}}"], "path": ["api", "projects"]}, "description": "Get all projects (requires admin/manager permission)"}}, {"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"client\": \"{{clientId}}\",\n    \"projectName\": \"Wedding Video Production\",\n    \"projectDescription\": \"Complete wedding video production with drone footage and editing\",\n    \"followupCallDate\": \"2025-06-15T10:00:00.000Z\",\n    \"startDate\": \"2025-06-20T00:00:00.000Z\",\n    \"endDate\": \"2025-06-25T00:00:00.000Z\",\n    \"status\": \"pending\",\n    \"stage\": \"quotation\"\n}"}, "url": {"raw": "{{baseUrl}}/api/projects", "host": ["{{baseUrl}}"], "path": ["api", "projects"]}, "description": "Create a new project with Zod validation", "event": [{"listen": "test", "script": {"exec": ["pm.test('Project creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('project');", "    pm.expect(response.data.project).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.project) {", "        const projectId = response.data.project._id;", "        pm.collectionVariables.set('projectId', projectId);", "        console.log('Project ID saved:', projectId);", "    } else {", "        console.error('Project creation failed: No project data received');", "    }", "} else {", "    console.error('Project creation failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}, "description": "Get a specific project by ID"}}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"projectName\": \"Wedding Video Production - Updated\",\n    \"projectDescription\": \"Complete wedding video production with drone footage, editing, and color grading\",\n    \"status\": \"in-progress\",\n    \"stage\": \"production\",\n    \"amountReceived\": 5000\n}"}, "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}"]}, "description": "Update an existing project"}}, {"name": "Update Project Status", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"completed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/projects/{{projectId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "projects", "{{projectId}}", "status"]}, "description": "Update only the project status"}}]}, {"name": "Templates", "description": "Template management endpoints with full CRUD operations", "item": [{"name": "Equipment Templates", "item": [{"name": "Get All Equipment Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment"]}, "description": "Get all equipment templates"}}, {"name": "Create Equipment Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Wedding Equipment Package\",\n    \"description\": \"Complete equipment package for wedding videography\",\n    \"items\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment"]}, "description": "Create a new equipment template", "event": [{"listen": "test", "script": {"exec": ["pm.test('Equipment template creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('template');", "    pm.expect(response.data.template).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.template) {", "        const templateId = response.data.template._id;", "        pm.collectionVariables.set('templateId', templateId);", "        console.log('Template ID saved:', templateId);", "    } else {", "        console.error('Template creation failed: No template data received');", "    }", "} else {", "    console.error('Template creation failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "Get Equipment Template by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}, "description": "Get a specific equipment template by ID"}}, {"name": "Update Equipment Template", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Updated Wedding Equipment Package\",\n    \"description\": \"Updated complete equipment package for wedding videography\",\n    \"items\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}, "description": "Update an existing equipment template"}}, {"name": "Delete Equipment Template", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment/{{templateId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment", "{{templateId}}"]}, "description": "Delete an equipment template (admin only)"}}]}, {"name": "Service Templates", "item": [{"name": "Get All Service Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/services", "host": ["{{baseUrl}}"], "path": ["api", "templates", "services"]}, "description": "Get all service templates"}}, {"name": "Create Service Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Wedding Video Services\",\n    \"description\": \"Complete video services package for weddings\",\n    \"items\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/services", "host": ["{{baseUrl}}"], "path": ["api", "templates", "services"]}, "description": "Create a new service template"}}]}, {"name": "Accessory Templates", "item": [{"name": "Get All Accessory Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/accessories", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessories"]}, "description": "Get all accessory templates"}}, {"name": "Create Accessory Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Camera Accessories Package\",\n    \"description\": \"Essential camera accessories for video production\",\n    \"items\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/accessories", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessories"]}, "description": "Create a new accessory template"}}]}, {"name": "Installation Templates", "item": [{"name": "Get All Installation Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/installations", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installations"]}, "description": "Get all installation templates"}}, {"name": "Create Installation Template", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"templateName\": \"Studio Setup Installation\",\n    \"description\": \"Complete studio installation services\",\n    \"items\": []\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/installations", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installations"]}, "description": "Create a new installation template"}}]}, {"name": "Accessory Items", "description": "Individual accessory item management with full CRUD operations", "item": [{"name": "Get All Accessory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/accessory-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items"]}, "description": "Get all accessory items (requires admin/manager permission)"}}, {"name": "Create Accessory Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"itemName\": \"Professional Tripod\",\n    \"quantity\": 5,\n    \"quantityType\": \"individual\",\n    \"amount\": 150.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/accessory-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items"]}, "description": "Create a new accessory item", "event": [{"listen": "test", "script": {"exec": ["pm.test('Accessory item creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('item');", "    pm.expect(response.data.item).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.item) {", "        const itemId = response.data.item._id;", "        pm.collectionVariables.set('accessoryItemId', itemId);", "        console.log('Accessory Item ID saved:', itemId);", "    } else {", "        console.error('Accessory item creation failed: No item data received');", "    }", "} else {", "    console.error('Accessory item creation failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}}, {"name": "Get Accessory Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/accessory-items/{{accessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items", "{{accessoryItemId}}"]}, "description": "Get a specific accessory item by ID"}}, {"name": "Update Accessory Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"itemName\": \"Professional Tripod - Updated\",\n    \"quantity\": 8,\n    \"quantityType\": \"individual\",\n    \"amount\": 180.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/accessory-items/{{accessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items", "{{accessoryItemId}}"]}, "description": "Update an existing accessory item"}}, {"name": "Search Accessory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/accessory-items/search?query=tripod", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items", "search"], "query": [{"key": "query", "value": "tripod"}]}, "description": "Search accessory items by name"}}, {"name": "Delete Accessory Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/accessory-items/{{accessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "accessory-items", "{{accessoryItemId}}"]}, "description": "Delete an accessory item (admin only)"}}]}, {"name": "Predefined Accessory Items", "description": "Predefined accessory item management for admin users", "item": [{"name": "Get All Predefined Accessory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items"]}, "description": "Get all predefined accessory items"}}, {"name": "Create Predefined Accessory Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"itemName\": \"Standard Camera Lens Set\",\n    \"quantity\": 3,\n    \"quantityType\": \"set\",\n    \"amount\": 750.00,\n    \"isPredefined\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items"]}, "description": "Create a new predefined accessory item (admin only)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Predefined accessory item creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('item');", "    pm.expect(response.data.item).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.item) {", "        const itemId = response.data.item._id;", "        pm.collectionVariables.set('predefinedAccessoryItemId', itemId);", "        console.log('Predefined Accessory Item ID saved:', itemId);", "    }", "}"], "type": "text/javascript"}}]}}, {"name": "Get Predefined Accessory Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items/{{predefinedAccessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items", "{{predefinedAccessoryItemId}}"]}, "description": "Get a specific predefined accessory item by ID"}}, {"name": "Update Predefined Accessory Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"itemName\": \"Premium Camera Lens Set\",\n    \"quantity\": 5,\n    \"quantityType\": \"set\",\n    \"amount\": 950.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items/{{predefinedAccessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items", "{{predefinedAccessoryItemId}}"]}, "description": "Update an existing predefined accessory item (admin only)"}}, {"name": "Search Predefined Accessory Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items/search?query=lens", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items", "search"], "query": [{"key": "query", "value": "lens"}]}, "description": "Search predefined accessory items by name"}}, {"name": "Delete Predefined Accessory Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-accessory-items/{{predefinedAccessoryItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-accessory-items", "{{predefinedAccessoryItemId}}"]}, "description": "Delete a predefined accessory item (admin only)"}}]}]}, {"name": "Equipment Items", "description": "Equipment item management for admin and manager users", "item": [{"name": "Get All Equipment Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items"]}, "description": "Get all equipment items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}, {"name": "Create Equipment Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemName\": \"Professional Camera\",\n  \"specification\": \"4K Ultra HD, 30fps\",\n  \"quantity\": 2,\n  \"quantityType\": \"individual\",\n  \"price\": 2500.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items"]}, "description": "Create a new equipment item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});", "", "pm.test('Save equipment item ID for future requests', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.success && responseJson.data && responseJson.data.item && responseJson.data.item._id) {", "        pm.collectionVariables.set('equipmentItemId', responseJson.data.item._id);", "        console.log('Equipment Item ID saved:', responseJson.data.item._id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Equipment Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment-items/{{equipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items", "{{equipmentItemId}}"]}, "description": "Get equipment item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Update Equipment Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemName\": \"Professional Camera - Updated\",\n  \"specification\": \"4K Ultra HD, 60fps\",\n  \"price\": 2750.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment-items/{{equipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items", "{{equipmentItemId}}"]}, "description": "Update equipment item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Search Equipment Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment-items/search?query=camera", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items", "search"], "query": [{"key": "query", "value": "camera"}]}, "description": "Search equipment items by name or specification"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has search structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data).to.have.property('searchQuery');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Equipment Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/equipment-items/{{equipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment-items", "{{equipmentItemId}}"]}, "description": "Delete an equipment item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Predefined Equipment Items", "description": "Predefined equipment item management for admin users", "item": [{"name": "Get All Predefined Equipment Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items"]}, "description": "Get all predefined equipment items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}, {"name": "Create Predefined Equipment Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemName\": \"Standard Camera Kit\",\n  \"specification\": \"Professional DSLR with basic accessories\",\n  \"quantity\": 1,\n  \"quantityType\": \"set\",\n  \"price\": 1500.00,\n  \"isPredefined\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items"]}, "description": "Create a new predefined equipment item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});", "", "pm.test('Save predefined equipment item ID for future requests', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.success && responseJson.data && responseJson.data.item && responseJson.data.item._id) {", "        pm.collectionVariables.set('predefinedEquipmentItemId', responseJson.data.item._id);", "        console.log('Predefined Equipment Item ID saved:', responseJson.data.item._id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Predefined Equipment Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items/{{predefinedEquipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items", "{{predefinedEquipmentItemId}}"]}, "description": "Get predefined equipment item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Update Predefined Equipment Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemName\": \"Premium Camera Kit\",\n  \"specification\": \"Professional DSLR with premium accessories\",\n  \"price\": 2000.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items/{{predefinedEquipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items", "{{predefinedEquipmentItemId}}"]}, "description": "Update predefined equipment item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Search Predefined Equipment Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items/search?query=camera", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items", "search"], "query": [{"key": "query", "value": "camera"}]}, "description": "Search predefined equipment items by name or specification"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has search structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data).to.have.property('searchQuery');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Predefined Equipment Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-equipment-items/{{predefinedEquipmentItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-equipment-items", "{{predefinedEquipmentItemId}}"]}, "description": "Delete a predefined equipment item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Installation Items", "description": "Installation item management for admin and manager users", "item": [{"name": "Get All Installation Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/installation-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items"]}, "description": "Get all installation items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}, {"name": "Create Installation Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Professional Studio Installation\",\n    \"price\": 500.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/installation-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items"]}, "description": "Create a new installation item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Installation item creation successful', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.success).to.be.true;", "    pm.expect(response.data).to.have.property('item');", "    pm.expect(response.data.item).to.have.property('_id');", "});", "", "if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data && response.data.item) {", "        const itemId = response.data.item._id;", "        pm.collectionVariables.set('installationItemId', itemId);", "        console.log('Installation Item ID saved:', itemId);", "    } else {", "        console.error('Installation item creation failed: No item data received');", "    }", "} else {", "    console.error('Installation item creation failed with status:', pm.response.code);", "}"], "type": "text/javascript"}}]}, {"name": "Get Installation Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/installation-items/{{installationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items", "{{installationItemId}}"]}, "description": "Get installation item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Update Installation Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Premium Studio Installation\",\n    \"price\": 750.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/installation-items/{{installationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items", "{{installationItemId}}"]}, "description": "Update an existing installation item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Search Installation Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/installation-items/search?query=studio", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items", "search"], "query": [{"key": "query", "value": "studio"}]}, "description": "Search installation items by name"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data).to.have.property('searchQuery');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Installation Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/installation-items/{{installationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "installation-items", "{{installationItemId}}"]}, "description": "Delete an installation item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Predefined Installation Items", "description": "Predefined installation item management for admin users", "item": [{"name": "Get All Predefined Installation Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items"]}, "description": "Get all predefined installation items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "});"], "type": "text/javascript"}}]}, {"name": "Create Predefined Installation Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Standard Studio Setup\",\n    \"price\": 400.00,\n    \"isPredefined\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items"]}, "description": "Create a new predefined installation item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});", "", "pm.test('Save predefined installation item ID for future requests', function () {", "    const responseJson = pm.response.json();", "    if (responseJson.success && responseJson.data && responseJson.data.item && responseJson.data.item._id) {", "        pm.collectionVariables.set('predefinedInstallationItemId', responseJson.data.item._id);", "        console.log('Predefined Installation Item ID saved:', responseJson.data.item._id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Predefined Installation Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items/{{predefinedInstallationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items", "{{predefinedInstallationItemId}}"]}, "description": "Get predefined installation item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Update Predefined Installation Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Premium Studio Setup Package\",\n    \"price\": 650.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items/{{predefinedInstallationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items", "{{predefinedInstallationItemId}}"]}, "description": "Update predefined installation item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('item');", "});"], "type": "text/javascript"}}]}, {"name": "Search Predefined Installation Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items/search?query=setup", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items", "search"], "query": [{"key": "query", "value": "setup"}]}, "description": "Search predefined installation items by name"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('count');", "    pm.expect(responseJson.data).to.have.property('items');", "    pm.expect(responseJson.data).to.have.property('searchQuery');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Predefined Installation Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-installation-items/{{predefinedInstallationItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-installation-items", "{{predefinedInstallationItemId}}"]}, "description": "Delete a predefined installation item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Service Items", "description": "Service Item management endpoints", "item": [{"name": "Get All Service Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/service-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items"]}, "description": "Get all service items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('count');", "});"], "type": "text/javascript"}}]}, {"name": "Create Service Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"serviceName\": \"Test Service\",\n    \"areaInSquareFeet\": 500,\n    \"pricePerSquareFoot\": 25.50\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/service-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items"]}, "description": "Create a new service item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Save the service item ID for other requests", "    if (responseJson.data && responseJson.data._id) {", "        pm.collectionVariables.set('serviceItemId', responseJson.data._id);", "        console.log('Service Item ID saved:', responseJson.data._id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Service Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/service-items/{{serviceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items", "{{serviceItemId}}"]}, "description": "Get a specific service item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('serviceName');", "    pm.expect(responseJson.data).to.have.property('areaInSquareFeet');", "    pm.expect(responseJson.data).to.have.property('pricePerSquareFoot');", "    pm.expect(responseJson.data).to.have.property('totalPrice');", "});"], "type": "text/javascript"}}]}, {"name": "Update Service Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"serviceName\": \"Updated Test Service\",\n    \"areaInSquareFeet\": 750,\n    \"pricePerSquareFoot\": 30.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/service-items/{{serviceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items", "{{serviceItemId}}"]}, "description": "Update a service item"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Search Service Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/service-items/search?query=Test", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items", "search"], "query": [{"key": "query", "value": "Test"}]}, "description": "Search service items by name"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('count');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Service Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/service-items/{{serviceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "service-items", "{{serviceItemId}}"]}, "description": "Delete a service item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Predefined Service Items", "description": "Predefined Service Item management endpoints", "item": [{"name": "Get All Predefined Service Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items"]}, "description": "Get all predefined service items"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('count');", "});"], "type": "text/javascript"}}]}, {"name": "Create Predefined Service Item", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"serviceName\": \"Standard Cleaning Service\",\n    \"areaInSquareFeet\": 1000,\n    \"pricePerSquareFoot\": 15.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items"]}, "description": "Create a new predefined service item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    ", "    // Save the predefined service item ID for other requests", "    if (responseJson.data && responseJson.data._id) {", "        pm.collectionVariables.set('predefinedServiceItemId', responseJson.data._id);", "        console.log('Predefined Service Item ID saved:', responseJson.data._id);", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Predefined Service Item by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items/{{predefinedServiceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items", "{{predefinedServiceItemId}}"]}, "description": "Get a specific predefined service item by ID"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data).to.have.property('serviceName');", "    pm.expect(responseJson.data).to.have.property('areaInSquareFeet');", "    pm.expect(responseJson.data).to.have.property('pricePerSquareFoot');", "    pm.expect(responseJson.data).to.have.property('totalPrice');", "});"], "type": "text/javascript"}}]}, {"name": "Update Predefined Service Item", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"serviceName\": \"Premium Cleaning Service\",\n    \"areaInSquareFeet\": 1200,\n    \"pricePerSquareFoot\": 20.00\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items/{{predefinedServiceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items", "{{predefinedServiceItemId}}"]}, "description": "Update a predefined service item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}, {"name": "Search Predefined Service Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items/search?query=Cleaning", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items", "search"], "query": [{"key": "query", "value": "Cleaning"}]}, "description": "Search predefined service items by name"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson).to.have.property('count');", "});"], "type": "text/javascript"}}]}, {"name": "Delete Predefined Service Item", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/templates/predefined-service-items/{{predefinedServiceItemId}}", "host": ["{{baseUrl}}"], "path": ["api", "templates", "predefined-service-items", "{{predefinedServiceItemId}}"]}, "description": "Delete a predefined service item (admin only)"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON><PERSON>).to.have.property('success', true);", "    pm.expect(responseJson).to.have.property('data');", "});"], "type": "text/javascript"}}]}]}, {"name": "Validation Examples", "description": "Examples of validation errors with <PERSON>od", "item": [{"name": "Client Validation Error Example", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"invalidField\": \"This will cause validation errors\",\n    \"email\": \"invalid-email\"\n}"}, "url": {"raw": "{{baseUrl}}/api/clients", "host": ["{{baseUrl}}"], "path": ["api", "clients"]}, "description": "Example request that will trigger Zod validation errors"}}, {"name": "Template Validation Error Example", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"This should be templateName\",\n    \"items\": [\"invalid-id-format\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/templates/equipment", "host": ["{{baseUrl}}"], "path": ["api", "templates", "equipment"]}, "description": "Example request that will trigger template validation errors"}}]}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check if the API server is running properly"}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Global pre-request script for Cinepanda API Collection", "", "// Check if we have a token for authenticated requests", "const token = pm.collectionVariables.get('token');", "if (!token && pm.request.url.toString().includes('/api/') && !pm.request.url.toString().includes('/auth/')) {", "    console.warn('Warning: No authentication token found. This request may fail if authentication is required.');", "}", "", "// Log the current request for debugging", "console.log(`Making ${pm.request.method} request to: ${pm.request.url.toString()}`);", "", "// Set base URL if not already set", "if (!pm.collectionVariables.get('baseUrl')) {", "    pm.collectionVariables.set('baseUrl', 'http://localhost:8000');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for Cinepanda API Collection", "", "// Common response validation", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON format', function () {", "    pm.response.to.be.json;", "});", "", "// Log response status and time", "console.log(`Response: ${pm.response.status} ${pm.response.code} (${pm.response.responseTime}ms)`);", "", "// Handle common error responses", "if (pm.response.code >= 400) {", "    const response = pm.response.json();", "    console.error('Error response:', response);", "    ", "    if (pm.response.code === 401) {", "        console.error('Authentication failed. Please check your token.');", "    }", "    ", "    if (pm.response.code === 403) {", "        console.error('Access forbidden. You may not have the required permissions.');", "    }", "}"]}}], "variable": [{"key": "token", "value": "", "type": "string", "description": "JWT token received after login"}, {"key": "baseUrl", "value": "http://localhost:8000", "type": "string", "description": "Base URL for the API"}, {"key": "clientId", "value": "", "type": "string", "description": "ID of a client for testing"}, {"key": "projectId", "value": "", "type": "string", "description": "ID of a project for testing"}, {"key": "templateId", "value": "", "type": "string", "description": "ID of a template for testing"}, {"key": "userId", "value": "", "type": "string", "description": "ID of the currently logged in user"}, {"key": "userRole", "value": "", "type": "string", "description": "Role of the currently logged in user"}, {"key": "accessoryItemId", "value": "", "type": "string", "description": "ID of an accessory item for testing"}, {"key": "predefinedAccessoryItemId", "value": "", "type": "string", "description": "ID of a predefined accessory item for testing"}, {"key": "equipmentItemId", "value": "", "type": "string", "description": "ID of an equipment item for testing"}, {"key": "predefinedEquipmentItemId", "value": "", "type": "string", "description": "ID of a predefined equipment item for testing"}, {"key": "installationItemId", "value": "", "type": "string", "description": "ID of an installation item for testing"}, {"key": "predefinedInstallationItemId", "value": "", "type": "string", "description": "ID of a predefined installation item for testing"}, {"key": "serviceItemId", "value": "", "type": "string", "description": "ID of a service item for testing"}, {"key": "predefinedServiceItemId", "value": "", "type": "string", "description": "ID of a predefined service item for testing"}]}