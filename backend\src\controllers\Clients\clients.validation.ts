import { z } from 'zod';

// Create client schema
export const createClientSchema = z.object({
  clientName: z.string({
    required_error: 'Client name is required',
    invalid_type_error: 'Client name must be a string',
  }).min(2, { message: 'Client name must be at least 2 characters long' })
    .max(100, { message: 'Client name cannot exceed 100 characters' }),
  number: z.string({
    required_error: 'Contact number is required',
    invalid_type_error: 'Contact number must be a string',
  }),
  email: z.string().email({ message: 'Please provide a valid email address' }).nullable().optional(),
  place: z.string({
    required_error: 'Place is required',
    invalid_type_error: 'Place must be a string',
  }).max(100, { message: 'Place cannot exceed 100 characters' }),
  address: z.string({
    required_error: 'Address is required',
    invalid_type_error: 'Address must be a string',
  }),
  description: z.string().nullable().optional(),
});

// Update client schema (similar to create but all fields optional)
export const updateClientSchema = z.object({
  clientName: z.string()
    .min(2, { message: 'Client name must be at least 2 characters long' })
    .max(100, { message: 'Client name cannot exceed 100 characters' })
    .optional(),
  number: z.string().optional(),
  email: z.string()
    .email({ message: 'Please provide a valid email address' })
    .nullable()
    .optional(),
  place: z.string()
    .max(100, { message: 'Place cannot exceed 100 characters' })
    .optional(),
  address: z.string().optional(),
  description: z.string().nullable().optional(),
});

// Client search schema
export const searchClientSchema = z.object({
  query: z.string({
    required_error: 'Search query is required',
    invalid_type_error: 'Search query must be a string',
  }).min(1, { message: 'Search query must be at least 1 character' }),
});

// Client ID parameter schema
export const clientIdSchema = z.object({
  id: z.string({
    required_error: 'Client ID is required',
    invalid_type_error: 'Client ID must be a string',
  })
  .length(24, { message: 'Invalid client ID format' })
  .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid client ID format' }),
});
