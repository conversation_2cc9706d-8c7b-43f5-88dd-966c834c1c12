import mongoose, { Schema, Document, Types } from 'mongoose';
import { IEquipmentItem } from './EquipmentItem'; // Assuming this path is correct

// Define an interface representing a document in MongoDB.
export interface IEquipmentTemplate extends Document {
  templateName: string;
  items: Types.ObjectId[] | IEquipmentItem[]; // Can be populated or just ObjectIds
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const EquipmentTemplateSchema: Schema<IEquipmentTemplate> = new Schema(
  {
    templateName: {
      type: String,
      required: [true, 'Template name is required'],
      trim: true,
    },
    items: [
      {
        type: Schema.Types.ObjectId,
        ref: 'EquipmentItem', // Reference to the EquipmentItem model
      },
    ],
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required'],
      default: 0,
      min: [0, 'Total amount cannot be negative'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
    // Consider adding a pre-save hook to calculate totalAmount if needed
    // or calculate it on the fly when querying.
  }
);

// Optional: Pre-save hook to calculate totalAmount from items.
// EquipmentTemplateSchema.pre('save', async function (next) {
//   if (this.isModified('items') || this.isNew) {
//     // If items are ObjectIds, you would need to fetch them to calculate the sum:
//     // const populatedItems = await mongoose.model('EquipmentItem').find({ '_id': { $in: this.items } });
//     // this.totalAmount = populatedItems.reduce((acc, currentItem) => acc + currentItem.price, 0);
//   }
//   next();
// });

// Create and export the Mongoose model.
const EquipmentTemplate = mongoose.model<IEquipmentTemplate>(
  'EquipmentTemplate',
  EquipmentTemplateSchema
);

export default EquipmentTemplate;
