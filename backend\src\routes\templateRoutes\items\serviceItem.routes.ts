import { Router } from 'express';
import {
    getAllServiceItems,
    getServiceItemById,
    createServiceItem,
    updateServiceItem,
    deleteServiceItem,
    searchServiceItems,
    getAllPredefinedServiceItems,
    getPredefinedServiceItemById,
    createPredefinedServiceItem,
    updatePredefinedServiceItem,
    deletePredefinedServiceItem,
    searchPredefinedServiceItems
  } from '../../../controllers/Templates/Items/serviceItem.controller';
import { restrictTo } from '../../../middleware/auth.middleware';

const serviceItemsRouter = Router();

serviceItemsRouter.route('/')
    .get(restrictTo('admin', 'manager', 'user'), getAllServiceItems)
    .post(restrictTo('admin', 'manager'), createServiceItem);

serviceItemsRouter.route('/search').get(restrictTo('admin', 'manager', 'user'), searchServiceItems);

serviceItemsRouter.route('/:id')
    .get(restrictTo('admin', 'manager', 'user'), getServiceItemById)
    .put(restrictTo('admin', 'manager'), updateServiceItem)
    .delete(restrictTo('admin', 'manager'), deleteServiceItem);

export default serviceItemsRouter;
