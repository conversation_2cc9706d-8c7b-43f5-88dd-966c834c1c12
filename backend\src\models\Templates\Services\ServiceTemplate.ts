import { Schema, model, Document, Types } from 'mongoose';
import { IServiceItem } from './ServiceItem'; // Assuming IServiceItem is exported from ServiceItem.ts

// Interface for the Service Template document
export interface IServiceTemplate extends Document {
  templateName: string;
  services: Types.ObjectId[] | IServiceItem[]; // Array of ServiceItem references
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Mongoose schema for Service Template
const ServiceTemplateSchema = new Schema<IServiceTemplate>(
  {
    templateName: {
      type: String,
      required: [true, 'Template name is required.'],
      trim: true,
      minlength: [3, 'Template name must be at least 3 characters long.'],
      maxlength: [100, 'Template name cannot exceed 100 characters.'],
    },
    services: [
      {
        type: Schema.Types.ObjectId,
        ref: 'ServiceItem', // Reference to the ServiceItem model
        required: true,
      },
    ],
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required.'],
      default: 0,
      min: [0, 'Total amount cannot be negative.'],
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    // TODO: Consider adding a pre-save hook to calculate totalAmount based on the services array if needed
  }
);

// Create and export the ServiceTemplate model
const ServiceTemplate = model<IServiceTemplate>('ServiceTemplate', ServiceTemplateSchema);

export default ServiceTemplate;
