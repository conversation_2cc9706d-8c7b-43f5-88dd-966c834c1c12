import { Router } from 'express';
import {
  getAllClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient,
  searchClients
} from '../controllers/Clients/clients.controller';
import { protect, restrictTo } from '../middleware/auth.middleware';

const router = Router();

// All client routes are protected
router.use(protect);

// Search route
router.get('/search', restrictTo('admin', 'manager'), searchClients);

// Main CRUD routes
router.route('/')
  .get(restrictTo('admin', 'manager'), getAllClients)
  .post(restrictTo('admin', 'manager'), createClient);

router.route('/:id')
  .get(restrictTo('admin', 'manager'), getClientById)
  .put(restrictTo('admin', 'manager'), updateClient)
  .delete(restrictTo('admin'), deleteClient); // Only admin can delete

export default router;
