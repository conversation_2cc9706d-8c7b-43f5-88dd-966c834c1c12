{"name": "cinepanda", "version": "1.0.0", "description": "Cinepanda Full-Stack Application", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start:backend": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2"}}