# Cinepanda API Testing Guide

This guide explains how to use the provided Postman collections to manually test the Cinepanda API endpoints.

## Prerequisites

- [<PERSON><PERSON>](https://www.postman.com/downloads/) installed on your system
- Cinepanda backend server running on localhost:8000

## Setup Instructions

1. **Import the Collection:**
   - Open Postman
   - Click on "Import" in the upper left corner
   - Select the file: `cinepanda.postman_collection.json` (or `auth.postman_collection.json` for auth endpoints only)
   - Click "Import" to add the collection to your workspace

2. **Start the Backend Server:**
   - Ensure MongoDB is running
   - Navigate to the backend directory: `cd backend`
   - Run the server: `npm run dev` or `npm start`

## Available Requests

The collection is organized into folders for each API category. The main collection includes:

1. Authentication endpoints
2. Projects endpoints (placeholder)
3. Clients endpoints (placeholder)
4. Templates endpoints (placeholder)
5. Health Check endpoint

### Authentication Endpoints

The auth section includes the following requests:
- Creates a regular user account
- Method: POST
- Endpoint: `http://localhost:8000/api/auth/register`
- Body:
  ```json
  {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "user"
  }
  ```

### 2. Register Admin User
- Creates an admin user account
- Method: POST
- Endpoint: `http://localhost:8000/api/auth/register`
- Body:
  ```json
  {
    "username": "admin",
    "email": "<EMAIL>",
    "password": "adminPass123",
    "role": "admin"
  }
  ```

### 3. Login User
- Authenticates a regular user
- Method: POST
- Endpoint: `http://localhost:8000/api/auth/login`
- Body:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```

### 4. Login Admin
- Authenticates an admin user
- Method: POST
- Endpoint: `http://localhost:8000/api/auth/login`
- Body:
  ```json
  {
    "email": "<EMAIL>",
    "password": "adminPass123"
  }
  ```

### 5. Get Current User
- Retrieves the authenticated user's details
- Method: GET
- Endpoint: `http://localhost:8000/api/auth/me`
- Headers:
  - Authorization: `Bearer {{token}}`

## Using the Collection

### Token Management

After successful login:
1. The response will include a JWT token
2. Copy the token value
3. Click on the collection name in the sidebar ("Cinepanda Auth API")
4. Click the "Variables" tab
5. Update the "token" variable's "CURRENT VALUE" with the copied token
6. Click "Save"

This will make the token available for the "Get Current User" request.

## Testing Workflow

1. Register a user
2. Login with the registered user credentials
3. Save the token from the login response
4. Use the "Get Current User" endpoint to verify authentication works

## Expected Responses

### Register User
```json
{
  "success": true,
  "message": "User registered successfully.",
  "data": {
    "user": {
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "_id": "...",
      "createdAt": "...",
      "updatedAt": "..."
    },
    "token": "..."
  }
}
```

### Login
```json
{
  "success": true,
  "message": "Login successful.",
  "data": {
    "user": {
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "_id": "...",
      "createdAt": "...",
      "updatedAt": "..."
    },
    "token": "..."
  }
}
```

### Get Current User
```json
{
  "success": true,
  "message": "User details fetched successfully.",
  "data": {
    "user": {
      "username": "testuser",
      "email": "<EMAIL>",
      "role": "user",
      "_id": "...",
      "createdAt": "...",
      "updatedAt": "..."
    }
  }
}
```

## Troubleshooting

1. **401 Unauthorized on Get Current User**
   - Verify you've set the token correctly in the collection variables
   - Check that the token hasn't expired (default expiry is 1 day)
   - Make sure the Authorization header is properly formatted as `Bearer your_token_here`

2. **Connection Refused**
   - Ensure the backend server is running on port 8000
   - Check your MongoDB connection

3. **Registration Fails with 409**
   - The email is already in use, try a different email address

4. **Login Fails with 401**
   - Verify the email and password are correct
   - Ensure the user exists in the database
