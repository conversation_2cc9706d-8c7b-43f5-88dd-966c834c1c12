import { z } from 'zod';

// Base template schema (shared across all template types)
const baseTemplateSchema = {
  templateName: z.string({
    required_error: 'Template name is required',
    invalid_type_error: 'Template name must be a string',
  }).min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' }),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).default([]),
};

// Base predefined template schema
const basePredefinedTemplateSchema = {
  name: z.string({
    required_error: 'Template name is required',
    invalid_type_error: 'Template name must be a string',
  }).min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' }),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).default([]),
  isPredefined: z.boolean().default(true).optional(),
};

// Template ID parameter schema
export const templateIdSchema = z.object({
  id: z.string({
    required_error: 'Template ID is required',
    invalid_type_error: 'Template ID must be a string',
  })
  .length(24, { message: 'Invalid template ID format' })
  .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid template ID format' }),
});

// Equipment Templates
export const createEquipmentTemplateSchema = z.object({
  ...baseTemplateSchema,
  // Add equipment-specific fields here if needed
});

export const updateEquipmentTemplateSchema = z.object({
  templateName: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  // Add equipment-specific fields here if needed
});

// Predefined Equipment Templates
export const createPredefinedEquipmentTemplateSchema = z.object({
  ...basePredefinedTemplateSchema,
});

export const updatePredefinedEquipmentTemplateSchema = z.object({
  name: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  isPredefined: z.boolean().optional(),
});

// Service Templates
export const createServiceTemplateSchema = z.object({
  ...baseTemplateSchema,
  // Add service-specific fields here if needed
});

export const updateServiceTemplateSchema = z.object({
  templateName: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  // Add service-specific fields here if needed
});

// Predefined Service Templates
export const createPredefinedServiceTemplateSchema = z.object({
  ...basePredefinedTemplateSchema,
});

export const updatePredefinedServiceTemplateSchema = z.object({
  name: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  isPredefined: z.boolean().optional(),
});

// Accessory Templates
export const createAccessoryTemplateSchema = z.object({
  ...baseTemplateSchema,
  // Add accessory-specific fields here if needed
});

export const updateAccessoryTemplateSchema = z.object({
  templateName: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  // Add accessory-specific fields here if needed
});

// Predefined Accessory Templates
export const createPredefinedAccessoryTemplateSchema = z.object({
  ...basePredefinedTemplateSchema,
});

export const updatePredefinedAccessoryTemplateSchema = z.object({
  name: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  isPredefined: z.boolean().optional(),
});

// Installation Templates
export const createInstallationTemplateSchema = z.object({
  ...baseTemplateSchema,
  // Add installation-specific fields here if needed
});

export const updateInstallationTemplateSchema = z.object({
  templateName: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  // Add installation-specific fields here if needed
});

// Predefined Installation Templates
export const createPredefinedInstallationTemplateSchema = z.object({
  ...basePredefinedTemplateSchema,
});

export const updatePredefinedInstallationTemplateSchema = z.object({
  name: z.string()
    .min(2, { message: 'Template name must be at least 2 characters long' })
    .max(100, { message: 'Template name cannot exceed 100 characters' })
    .optional(),
  description: z.string().nullable().optional(),
  items: z.array(
    z.string()
      .length(24, { message: 'Invalid item ID format' })
      .regex(/^[0-9a-fA-F]{24}$/, { message: 'Invalid item ID format' })
  ).optional(),
  isPredefined: z.boolean().optional(),
});
