#!/bin/bash
# Cinepanda Auth API Test Script
# This script tests the authentication endpoints using curl

# Configuration
API_URL="http://localhost:8000/api"
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Cinepanda Auth API Test Script ===${NC}"
echo -e "${BLUE}This script will test the authentication API endpoints${NC}"
echo

# Function to make API requests and format the response
function make_request() {
  local method=$1
  local endpoint=$2
  local data=$3
  local auth_header=$4
  
  echo -e "${YELLOW}Testing: ${method} ${endpoint}${NC}"
  echo -e "Request body: ${data}"
  
  local headers="-H 'Content-Type: application/json'"
  if [ ! -z "$auth_header" ]; then
    headers="$headers -H 'Authorization: Bearer $auth_header'"
  fi
  
  if [ "$method" == "GET" ]; then
    if [ ! -z "$auth_header" ]; then
      response=$(curl -s -X ${method} -H "Authorization: Bearer ${auth_header}" "${API_URL}${endpoint}")
    else
      response=$(curl -s -X ${method} "${API_URL}${endpoint}")
    fi
  else
    if [ ! -z "$auth_header" ]; then
      response=$(curl -s -X ${method} -H "Content-Type: application/json" -H "Authorization: Bearer ${auth_header}" -d "${data}" "${API_URL}${endpoint}")
    else
      response=$(curl -s -X ${method} -H "Content-Type: application/json" -d "${data}" "${API_URL}${endpoint}")
    fi
  fi
  
  # Format the JSON response for better readability using Python if available
  if command -v python &> /dev/null; then
    echo "$response" | python -m json.tool
  else
    echo "$response"
  fi
  
  echo
}

# 1. Register a test user
register_data='{"username": "apitestuser", "email": "<EMAIL>", "password": "apitest123", "role": "user"}'
echo -e "${GREEN}1. Registering a test user${NC}"
make_request "POST" "/auth/register" "$register_data"

# 2. Register an admin user
admin_register_data='{"username": "apiadmin", "email": "<EMAIL>", "password": "apiadmin123", "role": "admin"}'
echo -e "${GREEN}2. Registering an admin user${NC}"
make_request "POST" "/auth/register" "$admin_register_data"

# 3. Login with test user
login_data='{"email": "<EMAIL>", "password": "apitest123"}'
echo -e "${GREEN}3. Logging in with test user${NC}"
login_response=$(curl -s -X POST -H "Content-Type: application/json" -d "$login_data" "${API_URL}/auth/login")
echo "$login_response" | python -m json.tool 2>/dev/null || echo "$login_response"

# Extract the token from the login response using grep and cut
# Note: This is a simple extraction that assumes a specific response format
token=$(echo "$login_response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$token" ]; then
  echo -e "${RED}Failed to extract token from login response. Can't continue with authenticated requests.${NC}"
  exit 1
fi

echo -e "${GREEN}Token extracted:${NC} ${token:0:20}... (truncated)"
echo

# 4. Get current user information using the token
echo -e "${GREEN}4. Getting current user information${NC}"
make_request "GET" "/auth/me" "" "$token"

# 5. Login with admin user
admin_login_data='{"email": "<EMAIL>", "password": "apiadmin123"}'
echo -e "${GREEN}5. Logging in with admin user${NC}"
admin_login_response=$(curl -s -X POST -H "Content-Type: application/json" -d "$admin_login_data" "${API_URL}/auth/login")
echo "$admin_login_response" | python -m json.tool 2>/dev/null || echo "$admin_login_response"

# Extract the admin token
admin_token=$(echo "$admin_login_response" | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -z "$admin_token" ]; then
  echo -e "${RED}Failed to extract admin token from login response.${NC}"
else
  # 6. Get admin user information
  echo -e "${GREEN}6. Getting admin user information${NC}"
  make_request "GET" "/auth/me" "" "$admin_token"
fi

echo -e "${BLUE}=== Test script complete ===${NC}"
echo -e "${YELLOW}Note: The script creates two users in the database if they don't exist:${NC}"
echo "  - Regular user: <EMAIL> / apitest123"
echo "  - Admin user: <EMAIL> / apiadmin123"
echo "You can use these credentials for further manual testing with Postman."
