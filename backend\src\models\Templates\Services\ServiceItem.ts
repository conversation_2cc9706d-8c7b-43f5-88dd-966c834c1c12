import mongoose, { Schema, Document } from 'mongoose';

// Define an interface representing a document in MongoDB.
export interface IServiceItem extends Document {
  serviceName: string;
  areaInSquareFeet: number;
  pricePerSquareFoot: number;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
  isPredefined: boolean;
}

// Define the schema corresponding to the document interface.
const ServiceItemSchema: Schema<IServiceItem> = new Schema(
  {
    serviceName: {
      type: String,
      required: [true, 'Service name is required'],
      trim: true,
    },
    areaInSquareFeet: {
      type: Number,
      required: [true, 'Area in square feet is required'],
      min: [0, 'Area cannot be negative'], // Assuming area cannot be negative, adjust if needed
    },
    pricePerSquareFoot: {
      type: Number,
      required: [true, 'Price per square foot is required'],
      min: [0, 'Price per square foot cannot be negative'],
    },
    totalPrice: {
      type: Number,
      required: [true, 'Total price is required'],
      min: [0, 'Total price cannot be negative'],
      // This could be calculated automatically using a pre-save hook
    },
    isPredefined: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Optional: Pre-save hook to calculate totalPrice from areaInSquareFeet and pricePerSquareFoot.
ServiceItemSchema.pre<IServiceItem>('save', function (next) {
  if (this.isModified('areaInSquareFeet') || this.isModified('pricePerSquareFoot') || this.isNew) {
    this.totalPrice = this.areaInSquareFeet * this.pricePerSquareFoot;
  }
  next();
});

// Create and export the Mongoose model.
const ServiceItem = mongoose.model<IServiceItem>(
  'ServiceItem',
  ServiceItemSchema
);

export default ServiceItem;
