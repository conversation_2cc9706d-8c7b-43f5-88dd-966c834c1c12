import { Request, Response } from 'express';
import { isValidObjectId } from 'mongoose';
import Project, { ProjectStatus, ProjectStage } from '../../models/Project';
import { sendError, sendSuccess } from '../../utils/responseHandlers';
import { 
  createProjectSchema, 
  updateProjectSchema,
  updateStatusSchema,
  projectIdSchema
} from './projects.validation';

/**
 * @description Get all projects
 * @route GET /api/projects
 * @access Protected (Admin, Manager)
 */
export const getAllProjects = async (req: Request, res: Response) => {
  try {
    const projects = await Project.find()
      .populate('client', 'clientName email')
      .sort({ createdAt: -1 });

    return sendSuccess(res, 200, {
      count: projects.length,
      projects,
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching projects', error);
  }
};

/**
 * @description Get a single project by ID
 * @route GET /api/projects/:id
 * @access Protected (<PERSON><PERSON>, Manager)
 */
export const getProjectById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!isValidObjectId(id)) {
      return sendError(res, 400, 'Invalid project ID');
    }

    const project = await Project.findById(id)
      .populate('client', 'clientName email number')
      .populate('phases');
    
    if (!project) {
      return sendError(res, 404, 'Project not found');
    }

    return sendSuccess(res, 200, { project });
  } catch (error: any) {
    return sendError(res, 500, 'Error fetching project', error);
  }
};

/**
 * @description Create a new project
 * @route POST /api/projects
 * @access Protected (Admin, Manager)
 */
export const createProject = async (req: Request, res: Response) => {  try {
    // Validate request body against schema
    const result = createProjectSchema.safeParse(req.body);
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { 
      client, 
      projectName, 
      projectDescription, 
      followupCallDate, 
      startDate, 
      endDate, 
      status = ProjectStatus.PENDING, 
      stage = ProjectStage.QUOTATION 
    } = result.data;

    // Ensure client exists and is valid MongoDB ID
    if (!isValidObjectId(client)) {
      return sendError(res, 400, 'Invalid client ID');
    }

    const newProject = await Project.create({
      client,
      projectName,
      projectDescription,
      followupCallDate,
      startDate,
      endDate,
      amountReceived: 0, // Default value
      status,
      stage,
    });

    // Populate the client details for the response
    const populatedProject = await Project.findById(newProject._id)
      .populate('client', 'clientName email');

    return sendSuccess(res, 201, { 
      message: 'Project created successfully', 
      project: populatedProject 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error creating project', error);
  }
};

/**
 * @description Update a project by ID
 * @route PUT /api/projects/:id
 * @access Protected (Admin, Manager)
 */
export const updateProject = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate project ID using Zod schema
    const idResult = projectIdSchema.safeParse({ id });
    if (!idResult.success) {
      const messages = idResult.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid project ID', { messages });
    }

    // Validate request body against schema
    const result = updateProjectSchema.safeParse(req.body);
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const updateData = result.data;

    // Check if the project exists
    const existingProject = await Project.findById(id);
    if (!existingProject) {
      return sendError(res, 404, 'Project not found');
    }

    // If client ID is provided, ensure it's valid
    if (updateData.client && !isValidObjectId(updateData.client)) {
      return sendError(res, 400, 'Invalid client ID');
    }

    const updatedProject = await Project.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('client', 'clientName email');

    return sendSuccess(res, 200, { 
      message: 'Project updated successfully', 
      project: updatedProject 
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating project', error);
  }
};

/**
 * @description Delete a project by ID
 * @route DELETE /api/projects/:id
 * @access Protected (Admin)
 */
export const deleteProject = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    if (!isValidObjectId(id)) {
      return sendError(res, 400, 'Invalid project ID');
    }

    const project = await Project.findByIdAndDelete(id);
    
    if (!project) {
      return sendError(res, 404, 'Project not found');
    }

    return sendSuccess(res, 200, { 
      message: 'Project deleted successfully',
      projectId: id
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error deleting project', error);
  }
};

/**
 * @description Update project status
 * @route PATCH /api/projects/:id/status
 * @access Protected (Admin, Manager)
 */
export const updateProjectStatus = async (req: Request, res: Response) => {  try {
    const { id } = req.params;
    
    // Validate project ID using Zod schema
    const idResult = projectIdSchema.safeParse({ id });
    if (!idResult.success) {
      const messages = idResult.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Invalid project ID', { messages });
    }

    // Validate status
    const result = updateStatusSchema.safeParse(req.body);
    if (!result.success) {
      const messages = result.error.issues.map((issue) => issue.message);
      return sendError(res, 400, 'Validation Error', { messages });
    }
    
    const { status } = result.data;

    const updatedProject = await Project.findByIdAndUpdate(
      id,
      { status },
      { new: true, runValidators: true }
    );

    if (!updatedProject) {
      return sendError(res, 404, 'Project not found');
    }

    return sendSuccess(res, 200, {
      message: 'Project status updated successfully',
      project: updatedProject
    });
  } catch (error: any) {
    return sendError(res, 500, 'Error updating project status', error);
  }
};
