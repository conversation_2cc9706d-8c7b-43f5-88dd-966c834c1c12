import { useState } from 'react'
import { <PERSON>, EyeOff, Lock, Mail } from 'lucide-react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import toast from 'react-hot-toast'

const Login = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const { login, loading } = useAuth()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await login(formData.email, formData.password)
      toast.success('Welcome back!')
      // Don't manually navigate - let PublicRoute handle the redirect
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message)
      } else {
        toast.error('Invalid email or password')
      }
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[var(--color-light-grey-blue)] to-[var(--color-white)] px-4">
      <div className="max-w-md w-full space-y-8">
        {/* Header Section */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 rounded-full bg-[var(--color-deep-blue)] flex items-center justify-center mb-4">
            <Mail className="h-6 w-6 text-[var(--color-white)]" />
          </div>
          <h2 className="text-3xl font-bold text-[var(--color-dark-indigo)]">
            Welcome Back
          </h2>
          <p className="mt-2 text-sm text-[var(--color-steel-blue)]">
            Sign in to your CinePanda account
          </p>
        </div>

        {/* Login Form */}
        <div className="bg-[var(--color-white)] rounded-xl shadow-xl border border-[var(--color-light-grey-blue)] p-8">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-3 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Enter your email address"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-[var(--color-dark-indigo)] mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full pl-10 pr-10 py-3 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent transition-colors bg-[var(--color-white)] text-[var(--color-dark-indigo)] placeholder-[var(--color-muted-blue-grey)]"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  ) : (
                    <Eye className="h-5 w-5 text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] transition-colors" />
                  )}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-[var(--color-white)] bg-[var(--color-deep-blue)] hover:bg-[var(--color-dark-indigo)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--color-deep-blue)] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Signing In...' : 'Sign In'}
              </button>
            </div>

            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-sm text-[var(--color-steel-blue)]">
                Don't have an account?{' '}
                <Link
                  to="/auth/register"
                  className="font-medium text-[var(--color-deep-blue)] hover:text-[var(--color-dark-indigo)] transition-colors"
                >
                  Sign up now
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default Login