// Cinepanda API Test Script using Node.js
// This script tests all API endpoints including auth and placeholders for future endpoints

const fetch = require('node-fetch');
const util = require('util');

const API_URL = 'http://localhost:8000/api';

// Console colors
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Helper function to make API requests
async function makeRequest(method, endpoint, data, authToken) {
  try {
    console.log(`${colors.yellow}Testing: ${method} ${endpoint}${colors.reset}`);
    if (data) {
      console.log(`Request body: ${JSON.stringify(data, null, 2)}`);
    }

    const headers = {
      'Content-Type': 'application/json'
    };

    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const options = {
      method,
      headers
    };

    if (data && method !== 'GET') {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${API_URL}${endpoint}`, options);
    const responseData = await response.json();

    console.log('\nResponse:');
    console.log(util.inspect(responseData, { colors: true, depth: null }));
    console.log();

    return responseData;
  } catch (error) {
    console.error(`${colors.red}Error with ${method} ${endpoint}:${colors.reset}`, error.message);
    return null;
  }
}

// Helper function to print section headers
function printSection(title) {
  console.log(`\n${colors.bright}${colors.blue}=== ${title} ===${colors.reset}\n`);
}

async function runTests() {
  console.log(`${colors.bright}${colors.blue}=====================================${colors.reset}`);
  console.log(`${colors.bright}${colors.blue}=== Cinepanda API Test Script ===${colors.reset}`);
  console.log(`${colors.bright}${colors.blue}=====================================${colors.reset}`);
  console.log(`\n${colors.cyan}This script will test the Cinepanda API endpoints${colors.reset}\n`);

  // Health check
  printSection('Health Check');
  await makeRequest('GET', '/health');

  // Auth tests
  printSection('Authentication Tests');
  
  console.log(`${colors.green}1. Registering a test user${colors.reset}`);
  const registerData = {
    username: 'apitestuser',
    email: '<EMAIL>',
    password: 'apitest123',
    role: 'user'
  };
  const registerResponse = await makeRequest('POST', '/auth/register', registerData);

  console.log(`${colors.green}2. Registering an admin user${colors.reset}`);
  const adminRegisterData = {
    username: 'apiadmin',
    email: '<EMAIL>',
    password: 'apiadmin123',
    role: 'admin'
  };
  const adminRegisterResponse = await makeRequest('POST', '/auth/register', adminRegisterData);

  console.log(`${colors.green}3. Logging in with test user${colors.reset}`);
  const loginData = {
    email: '<EMAIL>',
    password: 'apitest123'
  };
  const loginResponse = await makeRequest('POST', '/auth/login', loginData);

  if (!loginResponse || !loginResponse.data || !loginResponse.data.token) {
    console.log(`${colors.red}Failed to get token from login response. Can't continue with authenticated requests.${colors.reset}`);
    return;
  }

  const token = loginResponse.data.token;
  console.log(`${colors.green}Token extracted:${colors.reset} ${token.substring(0, 20)}... (truncated)\n`);

  console.log(`${colors.green}4. Getting current user information${colors.reset}`);
  const currentUserResponse = await makeRequest('GET', '/auth/me', null, token);
  // Projects tests (implemented)
  printSection('Projects Tests');
  console.log(`${colors.green}5. Getting all projects${colors.reset}`);
  const projectsResponse = await makeRequest('GET', '/projects', null, token);
  
  console.log(`${colors.green}6. Creating a new project${colors.reset}`);
  const createProjectData = {
    projectName: `Test Project ${Date.now()}`,
    projectDescription: 'Test project created by API test script',
    status: 'Pending',
    stage: 'quotation'
  };
  // Note: This will fail if no clients exist in the database yet
  // We'd need to create a client first in a real scenario
  const createProjectResponse = await makeRequest('POST', '/projects', createProjectData, token);
  // Clients tests (implemented)
  printSection('Clients Tests');
  console.log(`${colors.green}7. Getting all clients${colors.reset}`);
  const clientsResponse = await makeRequest('GET', '/clients', null, token);
  
  console.log(`${colors.green}8. Creating a new client${colors.reset}`);
  const createClientData = {
    clientName: `Test Client ${Date.now()}`,
    number: '9876543210',
    email: `client_${Date.now()}@example.com`,
    place: 'Test City',
    address: '123 Test Street, Test City',
    description: 'Test client for API testing'
  };
  const createClientResponse = await makeRequest('POST', '/clients', createClientData, token);
  
  // Save client ID for other tests if creation was successful
  let clientId;
  if (createClientResponse && createClientResponse.success && createClientResponse.data.client) {
    clientId = createClientResponse.data.client._id;
    
    console.log(`${colors.green}9. Getting client by ID${colors.reset}`);
    const clientByIdResponse = await makeRequest('GET', `/clients/${clientId}`, null, token);
    
    console.log(`${colors.green}10. Searching clients${colors.reset}`);
    const searchResponse = await makeRequest('GET', `/clients/search?query=Test`, null, token);
  }
  // Templates tests (implemented)
  printSection('Templates Tests');
  
  // Equipment templates
  console.log(`${colors.green}11. Getting all equipment templates${colors.reset}`);
  const equipmentTemplatesResponse = await makeRequest('GET', '/templates/equipment', null, token);
  
  console.log(`${colors.green}12. Creating equipment template${colors.reset}`);
  const createEquipmentTemplateData = {
    name: `Equipment Template ${Date.now()}`,
    description: 'Test equipment template created by API script',
    items: [] // Would need to create items first in a real scenario
  };
  const createEquipmentResponse = await makeRequest('POST', '/templates/equipment', createEquipmentTemplateData, token);
  
  // Get template ID if creation was successful
  let templateId;
  if (createEquipmentResponse && createEquipmentResponse.success && createEquipmentResponse.data.template) {
    templateId = createEquipmentResponse.data.template._id;
    
    console.log(`${colors.green}13. Getting equipment template by ID${colors.reset}`);
    const templateByIdResponse = await makeRequest('GET', `/templates/equipment/${templateId}`, null, token);
    
    console.log(`${colors.green}14. Getting predefined equipment templates${colors.reset}`);
    const predefinedResponse = await makeRequest('GET', '/templates/equipment/predefined', null, token);
  }
  
  // Service templates
  console.log(`${colors.green}15. Getting all service templates${colors.reset}`);
  const serviceTemplatesResponse = await makeRequest('GET', '/templates/services', null, token);
  
  // Accessory templates
  console.log(`${colors.green}16. Getting all accessory templates${colors.reset}`);
  const accessoryTemplatesResponse = await makeRequest('GET', '/templates/accessories', null, token);
  
  // Installation templates
  console.log(`${colors.green}17. Getting all installation templates${colors.reset}`);
  const installationTemplatesResponse = await makeRequest('GET', '/templates/installations', null, token);

  printSection('Test script complete');
  console.log(`${colors.yellow}Note: The script creates two users in the database if they don't exist:${colors.reset}`);
  console.log('  - Regular user: <EMAIL> / apitest123');
  console.log('  - Admin user: <EMAIL> / apiadmin123');
  console.log('\nYou can use these credentials for further manual testing with Postman.');
  console.log('\nSome endpoints may return 404 errors if they have not been implemented yet.');
}

// Run the tests
runTests().catch(error => {
  console.error(`${colors.red}Unexpected error:${colors.reset}`, error);
});
