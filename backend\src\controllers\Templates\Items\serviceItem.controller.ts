import { Request, Response } from 'express';
import ServiceItem from '../../../models/Templates/Services/ServiceItem';
import PredefinedServiceItem from '../../../models/Templates/Services/Predefined/PredefinedServiceItem';
import {
  createServiceItemSchema,
  updateServiceItemSchema,
  serviceItemIdSchema,
  searchServiceItemSchema,
  createPredefinedServiceItemSchema,
  updatePredefinedServiceItemSchema,
} from './serviceItem.validation';
import { ZodError } from 'zod';

// ===== SERVICE ITEM CONTROLLERS =====

/**
 * Create a new service item
 */
export const createServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {

    console.log('createServiceItem...............', req.body);

    // Validate request body
    const validatedData = createServiceItemSchema.parse(req.body);

    // Check if service item with the same name already exists
    const existingItem = await ServiceItem.findOne({
      serviceName: { $regex: new RegExp(`^${validatedData.serviceName}$`, 'i') }
    });

    if (existingItem) {
      res.status(400).json({
        success: false,
        message: 'Service item with this name already exists',
      });
      return;
    }    // Create new service item
    const newServiceItem = new ServiceItem(validatedData);
    const savedServiceItem = await newServiceItem.save();

    res.status(201).json({
      success: true,
      message: 'Service item created successfully',
      data: savedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors,
      });
    } else if (error instanceof Error && error.name === 'ValidationError') {
      res.status(400).json({
        success: false,
        message: 'Database validation error',
        error: error.message,
      });
    } else {
      console.error('Error creating service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Get all service items
 */
export const getAllServiceItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const serviceItems = await ServiceItem.find().sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: 'Service items retrieved successfully',
      data: serviceItems,
      count: serviceItems.length,
    });
  } catch (error) {
    console.error('Error fetching service items:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Get service item by ID
 */
export const getServiceItemById = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters
    const { id } = serviceItemIdSchema.parse(req.params);

    const serviceItem = await ServiceItem.findById(id);

    if (!serviceItem) {
      res.status(404).json({
        success: false,
        message: 'Service item not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Service item retrieved successfully',
      data: serviceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid service item ID',
        errors: error.errors,
      });
    } else {
      console.error('Error fetching service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Update service item by ID
 */
export const updateServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters and body
    const { id } = serviceItemIdSchema.parse(req.params);
    const validatedData = updateServiceItemSchema.parse(req.body);

    // Check if another service item with the same name exists (if name is being updated)
    if (validatedData.serviceName) {
      const existingItem = await ServiceItem.findOne({
        serviceName: { $regex: new RegExp(`^${validatedData.serviceName}$`, 'i') },
        _id: { $ne: id },
      });

      if (existingItem) {
        res.status(400).json({
          success: false,
          message: 'Another service item with this name already exists',
        });
        return;
      }
    }

    const updatedServiceItem = await ServiceItem.findByIdAndUpdate(
      id,
      validatedData,
      { new: true, runValidators: true }
    );

    if (!updatedServiceItem) {
      res.status(404).json({
        success: false,
        message: 'Service item not found',
      }); return;
    }

    res.status(200).json({
      success: true,
      message: 'Service item updated successfully',
      data: updatedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors,
      });
    } else if (error instanceof Error && error.name === 'ValidationError') {
      res.status(400).json({
        success: false,
        message: 'Database validation error',
        error: error.message,
      });
    } else {
      console.error('Error updating service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Delete service item by ID
 */
export const deleteServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters
    const { id } = serviceItemIdSchema.parse(req.params);

    const deletedServiceItem = await ServiceItem.findByIdAndDelete(id);

    if (!deletedServiceItem) {
      res.status(404).json({
        success: false,
        message: 'Service item not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Service item deleted successfully',
      data: deletedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid service item ID',
        errors: error.errors,
      });
    } else {
      console.error('Error deleting service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Search service items by name
 */
export const searchServiceItems = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate query parameters
    const { query } = searchServiceItemSchema.parse(req.query);

    const serviceItems = await ServiceItem.find({
      serviceName: { $regex: new RegExp(query, 'i') },
    }).sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: 'Service items search completed',
      data: serviceItems,
      count: serviceItems.length,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid search query',
        errors: error.errors,
      });
    } else {
      console.error('Error searching service items:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

// ===== PREDEFINED SERVICE ITEM CONTROLLERS =====

/**
 * Create a new predefined service item
 */
export const createPredefinedServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {

    console.log('createPredefinedServiceItem', req.body);

    // Validate request body
    const validatedData = createPredefinedServiceItemSchema.parse(req.body);

    // Check if predefined service item with the same name already exists
    const existingItem = await PredefinedServiceItem.findOne({
      serviceName: { $regex: new RegExp(`^${validatedData.serviceName}$`, 'i') }
    });

    if (existingItem) {
      res.status(400).json({
        success: false,
        message: 'Predefined service item with this name already exists',
      });
      return;
    }

    // Create new predefined service item
    const newPredefinedServiceItem = new PredefinedServiceItem(validatedData);
    const savedPredefinedServiceItem = await newPredefinedServiceItem.save();

    res.status(201).json({
      success: true,
      message: 'Predefined service item created successfully',
      data: savedPredefinedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors,
      });
    } else if (error instanceof Error && error.name === 'ValidationError') {
      res.status(400).json({
        success: false,
        message: 'Database validation error',
        error: error.message,
      });
    } else {
      console.error('Error creating predefined service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Get all predefined service items
 */
export const getAllPredefinedServiceItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const predefinedServiceItems = await PredefinedServiceItem.find().sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: 'Predefined service items retrieved successfully',
      data: { count: predefinedServiceItems.length, items: predefinedServiceItems },
    });
  } catch (error) {
    console.error('Error fetching predefined service items:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Get predefined service item by ID
 */
export const getPredefinedServiceItemById = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters
    const { id } = serviceItemIdSchema.parse(req.params);

    const predefinedServiceItem = await PredefinedServiceItem.findById(id);

    if (!predefinedServiceItem) {
      res.status(404).json({
        success: false,
        message: 'Predefined service item not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Predefined service item retrieved successfully',
      data: predefinedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid predefined service item ID',
        errors: error.errors,
      });
    } else {
      console.error('Error fetching predefined service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Update predefined service item by ID
 */
export const updatePredefinedServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters and body
    const { id } = serviceItemIdSchema.parse(req.params);
    const validatedData = updatePredefinedServiceItemSchema.parse(req.body);

    // Check if another predefined service item with the same name exists (if name is being updated)
    if (validatedData.serviceName) {
      const existingItem = await PredefinedServiceItem.findOne({
        serviceName: { $regex: new RegExp(`^${validatedData.serviceName}$`, 'i') },
        _id: { $ne: id },
      });

      if (existingItem) {
        res.status(400).json({
          success: false,
          message: 'Another predefined service item with this name already exists',
        });
        return;
      }
    }

    const updatedPredefinedServiceItem = await PredefinedServiceItem.findByIdAndUpdate(
      id,
      validatedData,
      { new: true, runValidators: true }
    );

    if (!updatedPredefinedServiceItem) {
      res.status(404).json({
        success: false,
        message: 'Predefined service item not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Predefined service item updated successfully',
      data: updatedPredefinedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors,
      });
    } else if (error instanceof Error && error.name === 'ValidationError') {
      res.status(400).json({
        success: false,
        message: 'Database validation error',
        error: error.message,
      });
    } else {
      console.error('Error updating predefined service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Delete predefined service item by ID
 */
export const deletePredefinedServiceItem = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request parameters
    const { id } = serviceItemIdSchema.parse(req.params);

    const deletedPredefinedServiceItem = await PredefinedServiceItem.findByIdAndDelete(id);

    if (!deletedPredefinedServiceItem) {
      res.status(404).json({
        success: false,
        message: 'Predefined service item not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Predefined service item deleted successfully',
      data: deletedPredefinedServiceItem,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid predefined service item ID',
        errors: error.errors,
      });
    } else {
      console.error('Error deleting predefined service item:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};

/**
 * Search predefined service items by name
 */
export const searchPredefinedServiceItems = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate query parameters
    const { query } = searchServiceItemSchema.parse(req.query);

    const predefinedServiceItems = await PredefinedServiceItem.find({
      serviceName: { $regex: new RegExp(query, 'i') },
    }).sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: 'Predefined service items search completed',
      data: predefinedServiceItems,
      count: predefinedServiceItems.length,
    });
  } catch (error) {
    if (error instanceof ZodError) {
      res.status(400).json({
        success: false,
        message: 'Invalid search query',
        errors: error.errors,
      });
    } else {
      console.error('Error searching predefined service items:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
};