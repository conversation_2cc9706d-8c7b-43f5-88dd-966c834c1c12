import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import User from '../models/User';
import { sendError } from '../utils/responseHandlers';

/**
 * Middleware to protect routes that require authentication
 */
export const protect = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response> => {
  let token;

  // Check for token in Authorization header
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as {
        id: string;
      };      // Get user from the token
      const user = await User.findById(decoded.id).select('-passwordHash');

      if (!user) {
        return sendError(res, 401, 'User not found with this token.');
      }

      // Set user on the request object
      req.user = {
        id: (user._id as mongoose.Types.ObjectId).toString(),
        username: user.username,
        email: user.email,
        role: user.role
      };

      next();
    } catch (error) {
      console.error('Authentication error:', error);
      return sendError(res, 401, 'Not authorized, invalid token.');
    }
  }

  if (!token) {
    return sendError(res, 401, 'Not authorized, no token provided.');
  }
};

/**
 * Middleware to restrict access by role
 * @param roles - Array of allowed roles
 */
export const restrictTo = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    if (!req.user || !req.user.role) {
      return sendError(res, 401, 'Not authenticated. Please log in.');
    }

    if (!roles.includes(req.user.role)) {
      return sendError(
        res, 
        403, 
        'You do not have permission to perform this action.'
      );
    }

    next();
  };
};
