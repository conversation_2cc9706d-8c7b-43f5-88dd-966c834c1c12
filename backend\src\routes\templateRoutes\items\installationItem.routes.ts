import { Router } from 'express';
import {
    getAllInstallationItems,
    getInstallationItemById,
    createInstallationItem,
    updateInstallationItem,
    deleteInstallationItem,
    searchInstallationItems,
    getAllPredefinedInstallationItems,
    getPredefinedInstallationItemById,
    createPredefinedInstallationItem,
    updatePredefinedInstallationItem,
    deletePredefinedInstallationItem,
    searchPredefinedInstallationItems
  } from '../../../controllers/Templates/Items/installationItem.controller';
import { restrictTo } from '../../../middleware/auth.middleware';

const installationItemsRouter = Router();

installationItemsRouter.route('/')
    .get(restrictTo('admin', 'manager', 'user'), getAllInstallationItems)
    .post(restrictTo('admin', 'manager'), createInstallationItem);

installationItemsRouter.route('/search').get(restrictTo('admin', 'manager', 'user'), searchInstallationItems);

installationItemsRouter.route('/:id')
    .get(restrictTo('admin', 'manager', 'user'), getInstallationItemById)
    .put(restrictTo('admin', 'manager'), updateInstallationItem)
    .delete(restrictTo('admin', 'manager'), deleteInstallationItem);

export default installationItemsRouter;
