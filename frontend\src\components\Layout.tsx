import { useState } from 'react'
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { 
  LayoutDashboard, 
  Users, 
  FolderOpen, 
  Settings, 
  LogOut, 
  Menu, 
  X,
  Bell,
  Search,
  FileText
} from 'lucide-react'

const Layout = () => {
  const { user, logout } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { name: 'Projects', href: '/projects', icon: FolderOpen },
    { name: 'Clients', href: '/clients', icon: Users },
    { name: 'Settings', href: '/settings', icon: Settings },
    { name: 'Predefined', href: '/predefined', icon: FileText },
  ]

  const handleLogout = () => {
    logout()
    navigate('/auth/login')
  }

  const isActive = (path: string) => location.pathname === path

  return (
    <div className="flex h-screen bg-[var(--color-light-grey-blue)]">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-[var(--color-white)] shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-[var(--color-light-grey-blue)]">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-[var(--color-deep-blue)] rounded-lg flex items-center justify-center">
              <span className="text-[var(--color-white)] font-bold text-sm">CP</span>
            </div>
            <span className="text-xl font-bold text-[var(--color-dark-indigo)]">CinePanda</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)]"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-3">
          <div className="space-y-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors ${
                  isActive(item.href)
                    ? 'bg-[var(--color-deep-blue)] text-[var(--color-white)]'
                    : 'text-[var(--color-steel-blue)] hover:bg-[var(--color-light-grey-blue)] hover:text-[var(--color-dark-indigo)]'
                }`}
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </div>
        </nav>

        {/* User Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-[var(--color-light-grey-blue)]">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center">
              <span className="text-[var(--color-white)] text-sm font-semibold">
                {user?.username?.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-[var(--color-dark-indigo)] truncate">{user?.username}</p> 
              <p className="text-xs text-[var(--color-muted-blue-grey)] truncate">{user?.email}</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-3 py-2 text-sm text-[var(--color-steel-blue)] hover:bg-[var(--color-light-grey-blue)] hover:text-[var(--color-dark-indigo)] rounded-lg transition-colors"
          >
            <LogOut className="mr-3 h-4 w-4" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header */}
        <header className="bg-[var(--color-white)] shadow-sm border-b border-[var(--color-light-grey-blue)]">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            {/* Mobile menu button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="lg:hidden text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)]"
            >
              <Menu className="h-6 w-6" />
            </button>

            {/* Search Bar */}
            <div className="flex-1 max-w-lg mx-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-[var(--color-muted-blue-grey)]" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full pl-10 pr-3 py-2 border border-[var(--color-light-grey-blue)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-deep-blue)] focus:border-transparent bg-[var(--color-light-grey-blue)] bg-opacity-50"
                />
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-4">
              <button className="text-[var(--color-muted-blue-grey)] hover:text-[var(--color-steel-blue)] relative">
                <Bell className="h-6 w-6" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
              </button>
              
              {/* User Avatar - Desktop */}
              <div className="hidden sm:flex items-center space-x-3">
                <div className="w-8 h-8 bg-[var(--color-deep-blue)] rounded-full flex items-center justify-center">
                  <span className="text-[var(--color-white)] text-sm font-semibold">
                    {user?.username?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium text-[var(--color-dark-indigo)]">{user?.username}</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          <Outlet />
        </main>
      </div>
    </div>
  )
}

export default Layout
